import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { getConnectionOptions } from 'typeorm';
import { OConstants } from './OConstants';
import { BaseLibraryModule, DataBaseService, SecurityMiddleware } from '@bryzos/base-library';
import { PdfMakerModule } from '@bryzos/pdf-maker';

@Module({
  imports: [
    BaseLibraryModule,
    DataBaseService,
    PdfMakerModule,
    TypeOrmModule.forRootAsync({
      useFactory: async () =>
        Object.assign(await getConnectionOptions(), {
          autoLoadEntities: true,
        }),
    }),
    TypeOrmModule.forFeature(OConstants.EntityArray),
  ],
  providers: [
    BaseLibraryModule,
    DataBaseService,
    ...OConstants.EntityArray,
    ...OConstants.ServiceArray,
  ],
  exports: [
    BaseLibraryModule,
    DataBaseService,
    ...OConstants.EntityArray,
    ...OConstants.ServiceArray,
  ],
})

export class SharedModule implements NestModule{
	configure(consumer: MiddlewareConsumer) {
		// consumer
		// .apply(SecurityMiddleware)
		// .exclude('external-affairs/getSellerDocuments','external-affairs/getBuyerDocuments','reference-data/getSecurityToken','user/purchaseOrderExcel','user/payableStatementExcel', 'user/salesOrderExcel', 'user/receivableStatementExcel')
		// .forRoutes('*')
	}
}
