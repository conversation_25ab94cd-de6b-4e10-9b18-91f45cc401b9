import { AwsUtilityV3, BaseLibraryService, DataBaseService } from "@bryzos/base-library";
import { User } from "@bryzos/extended-widget-library";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository } from "typeorm";
import { Constants } from "./Constants";

const axios = require('axios');
//TODO : call getBuyerId and getBalanceKey from getBuyerCreditLimit()
export class Balance {
    private dbServiceObj = new DataBaseService()
    
    constructor(private readonly awsUtility:AwsUtilityV3,
        private readonly base: BaseLibraryService,
        @InjectRepository(User) private readonly userRepository: Repository<User>,) {}


    getBuyerCreditLimit = (balanceBuyerId, balanceKey)  => new Promise(async function (resolve, reject) {
        let buyerLimit = [];
        buyerLimit['balance_credit_limit'] = 0;
        buyerLimit['balance_available_credit_limit'] = 0;
        const getQualificationLinkUrl = process.env.BALANCE_QUALIFICATION_BASE_URL+'/'+balanceBuyerId+'/creditLimit';

        let data = {
            method: 'get',
            url: getQualificationLinkUrl,
            headers: {
              'Content-Type': 'application/json',
              'x-api-key': balanceKey,
            },
        };
        axios.request(data)
        .then((response) => {
            let balanceResponse = response.data;
            buyerLimit['balance_credit_limit'] = Math.round(balanceResponse.maxCreditLimit / 100 * 100) / 100;
            buyerLimit['balance_available_credit_limit'] = Math.round(balanceResponse.creditLimit / 100 * 100) / 100;
            resolve(buyerLimit);
        })
        .catch((error) => {
            console.error(error.response.data);
            resolve(buyerLimit);
        });
    });

    async getBuyerId(userId) {
        //get user unique key
        let buyerId = null;
        let user = await this.dbServiceObj.findOne(this.userRepository, 'id', userId);
        if(user == undefined) { return null; }
        if(user.unique_key == undefined) { return null; }
        let uniqueKey = user.unique_key + '-' + Constants.BALANCE_BUYER_ID;
        let response = await this.awsUtility.getS3Element(uniqueKey,process.env.PAYMENT_BUCKET);
        if(response != undefined) {
            let s3 = JSON.parse(response);
            buyerId = s3['balance_buyer_id'];
        }
        return buyerId;
    }

    async getBalanceKey() {
        return await this.base.getSecretValue(process.env.SM_ENV,Constants.VENDOR_BALANCE, Constants.BALANCE_SECRET_KEY);
    }
}