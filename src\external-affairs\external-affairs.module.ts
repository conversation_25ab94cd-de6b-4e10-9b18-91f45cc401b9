import { Module } from '@nestjs/common';
import { ExternalAffairsController } from './external-affairs.controller';
import { SharedModule } from 'src/shared.module';

@Module({
  imports: [SharedModule],
  controllers: [ExternalAffairsController],
  providers: [SharedModule],
  exports: [SharedModule],
})

// @Module({
//   imports : [BaseLibraryModule,DataBaseService,TypeOrmModule.forFeature(OConstants.EntityArray)],
//   controllers: [ExternalAffairsController],
//   providers: OConstants.ServiceArray

// })
export class ExternalAffairsModule {}
