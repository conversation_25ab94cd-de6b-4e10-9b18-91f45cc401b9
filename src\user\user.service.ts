import { AwsUtilityV3, DataBaseService, CommonService, BaseLibraryService, ReferenceDataSettings } from '@bryzos/base-library';
import { CompanyBuyNowPayLater, PaymentInfo, ReferenceDataSalesTax, UserArPaymentInfo, UserBuyingPreference, UserDeleteAccount, UserOrderRatings, UserProductTagMapping, UserPurchaseOrder, UserSellingPreference } from '@bryzos/extended-widget-library';
import { ReferenceDataBryzosTermsConditions, User, UserLogger, UserPricingFeedback, UserShareProductPricing, UserShareWidgetRequest, WidgetTermsCondtionUserActions, UserResaleCertificate, ReferenceDataStates,ReferenceDataOrderStatus, UserCreatepoOpenClose, UserSearchAnalytics, UserOnboradPendingRequests,UserSearchLineDetails,ReferenceDataProductsWidget,UserPurchaseOrderLine, ReferenceDataStateZipcode, UserMainCompany, UserPendingCompanyRequests,UserSearchPriceDetails,UserSearchPriceScreenMoveOut, ChatUsers } from '@bryzos/extended-widget-library';
import { Injectable, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { format, parseISO } from 'date-fns';
import { utcToZonedTime } from 'date-fns-tz';
import { AwsQueue } from 'src/AwsQueue';
import { Constants } from 'src/Constants';
import { PORatingsDto } from 'src/purchase-order/dto/save-po-ratings.dto';
import { Repository, Connection } from 'typeorm';
import { GetSignedUrl, PoS3UrlsDto, PricingFeedbackDto, ShareProductPricing, ShareWidgetRequest, UserLoginDto, WidgetTermsCondtionUserActionsDto, OnBoardDto, UserEmailDto, TooltipDto, ForegroundBackgroundActivityDto } from './dto/user.dto';

import { Workbook } from 'exceljs';
import * as tmp from 'tmp';
import { resolve } from 'path';
import { BryzosLogger } from "@bryzos/extended-widget-library";

import { Utility } from 'src/utility';
import { PdfMakerService } from '@bryzos/pdf-maker';
import { ChannelAddUsersDto, ChatRoomDto } from './dto/chat.dto';
import { CreateUserDto } from './dto/create-user.dto';
const axios = require('axios');

@Injectable()
export class UserService {
  baseUrl = process.env.DEAD_SIMPLE_CHAT_BASE_URL;
  params = { auth: process.env.DEAD_SIMPLE_CHAT_PRIVATE_KEY };
  roomId = process.env.DEAD_SIMPLE_CHAT_ROOM_ID;


  private dbServiceObj = new DataBaseService()

  constructor(private readonly awsQueue:AwsQueue,
    private readonly connection: Connection,
    private readonly commonLibraryService: CommonService,
    private readonly baseLibraryService: BaseLibraryService,
    private readonly pdfMakerService: PdfMakerService,
    @InjectRepository(User) private readonly userRepository: Repository<User>,
    @InjectRepository(UserPricingFeedback) private readonly pricingFeedback: Repository<UserPricingFeedback>,
    @InjectRepository(UserShareWidgetRequest) private readonly shareWidgetRequest: Repository<UserShareWidgetRequest>,
    @InjectRepository(UserLogger) private readonly userLogger: Repository<UserLogger>,
    @InjectRepository(UserShareProductPricing) private readonly userShareProductPricing: Repository<UserShareProductPricing>,
    @InjectRepository(ReferenceDataBryzosTermsConditions) private readonly refBryzosTermsCondition: Repository<ReferenceDataBryzosTermsConditions>,
    @InjectRepository(WidgetTermsCondtionUserActions) private readonly widgetTermsConditionRepository: Repository<WidgetTermsCondtionUserActions>,
    @InjectRepository(UserPurchaseOrder) private readonly userPurchaseOrderRepository: Repository<UserPurchaseOrder>,
    @InjectRepository(UserOrderRatings) private readonly userOrderRatingsRepository: Repository<UserOrderRatings>,
    @InjectRepository(UserResaleCertificate) private readonly userResaleCertificateRepository: Repository<UserResaleCertificate>,
    @InjectRepository(UserProductTagMapping) private readonly userProductTagMappingRepository: Repository<UserProductTagMapping>,
    @InjectRepository(ReferenceDataOrderStatus) private readonly userReferenceDataOrderStatus: Repository<ReferenceDataOrderStatus>,
    @InjectRepository(ReferenceDataStates) private readonly referenceDataStatesRepository: Repository<ReferenceDataStates>,
    @InjectRepository(UserCreatepoOpenClose) private readonly userCreatepoOpenClose: Repository<UserCreatepoOpenClose>,
    @InjectRepository(UserSearchAnalytics) private readonly userSearchAnalytics: Repository<UserSearchAnalytics>,
    @InjectRepository(UserOnboradPendingRequests) private readonly userOnboradPendingRequestsRepository: Repository<UserOnboradPendingRequests>,
    @InjectRepository(UserSearchLineDetails) private readonly userSearchLineDetailsRepository: Repository<UserSearchLineDetails>,
    @InjectRepository(ReferenceDataProductsWidget) private readonly referenceDataProductsWidgetRepository: Repository<ReferenceDataProductsWidget>,
    @InjectRepository(ReferenceDataSettings) private readonly referenceDataSettingsRepository: Repository<ReferenceDataSettings>,
    @InjectRepository(UserPurchaseOrderLine) private readonly userPurchaseOrderLineRepository: Repository<UserPurchaseOrderLine>,
    @InjectRepository(ReferenceDataStateZipcode) private readonly referenceDataStateZipcodeRepository: Repository<ReferenceDataStateZipcode>,
    @InjectRepository(UserMainCompany) private readonly userMainCompanyRepository: Repository<UserMainCompany>,
    @InjectRepository(UserPendingCompanyRequests) private readonly userPendingCompanyRequestsRepository: Repository<UserPendingCompanyRequests>,
    @InjectRepository(UserSearchPriceDetails) private readonly userSearchPriceDetailsRepository: Repository<UserSearchPriceDetails>,
    @InjectRepository(UserSearchPriceScreenMoveOut) private readonly userSearchPriceScreenMoveOutRepository: Repository<UserSearchPriceScreenMoveOut>,
    @InjectRepository(UserBuyingPreference) private readonly userBuyingPreferenceRepository: Repository<UserBuyingPreference>,
    @InjectRepository(CompanyBuyNowPayLater) private readonly companyBuyNowPayLaterRepository: Repository<CompanyBuyNowPayLater>,
    @InjectRepository(UserArPaymentInfo) private readonly userArPaymentInfoRepository: Repository<UserArPaymentInfo>,
    @InjectRepository(PaymentInfo) private readonly paymentInfoRepository: Repository<PaymentInfo>,
    @InjectRepository(UserSellingPreference) private readonly userSellingPreferenceRepository: Repository<UserSellingPreference>,
    @InjectRepository(UserDeleteAccount) private readonly userDeleteAccountRepository: Repository<UserDeleteAccount>,
    @InjectRepository(ChatUsers) private readonly chatUsersRepository: Repository<ChatUsers>,
    @InjectRepository(ReferenceDataSalesTax) private readonly referenceDataSalesTaxRepository: Repository<ReferenceDataSalesTax>,
    
    


    //
    private readonly awsUtility:AwsUtilityV3
  ) {}

  async login(userId, loginDto: UserLoginDto) {
    let response = null;
    const deviceId = loginDto.device_id;
  
    let user = await this.dbServiceObj.findOne(this.userRepository, 'id', userId);

    if(user !== undefined) {
            
       response = {
        ...user,
        disc_is_discounted: user.is_buyer_spread,
        disc_discount_pricing_column: user.base_pricing_column,
        disc_discount_rate: user.buyer_spread_rate,
        disc_is_discount_var_overriden: user.is_buyer_spread_overriden,
        disc_discount_period: user.deprecated_disc_discount_period,
        disc_discount_phaseout_startdate: user.deprecated_disc_discount_phaseout_startdate,
        disc_discount_phaseout_period: user.deprecated_disc_discount_phaseout_period,

      };

      //get current tnc version
      let tncVersion = await this.dbServiceObj.findOneByMultipleWhere(this.refBryzosTermsCondition,{type:user.type});
      if(tncVersion != undefined) {
        response.current_tnc_version = tncVersion.terms_conditions_version;
      }

      // get chat_user data
      const chatUserData = await this.dbServiceObj.findOneByMultipleWhere(this.chatUsersRepository, { external_user_id: user.id });
      if (chatUserData) {
        const isModerator = !!chatUserData.is_moderator;
        response['chat_data'] = {
          unique_user_identifier: chatUserData.unique_user_identifier,
          access_token: isModerator ? chatUserData.access_token : null,
          is_moderator: isModerator,
          user_id: chatUserData.id,
        }
      }

      //update last login for this user
      let currentDate = new Date();
      const utcDate = utcToZonedTime(currentDate, 'UTC');
      let lastLogin = format(utcDate, 'yyyy-MM-dd HH:mm:ss');

      const mappedDeviceIds = JSON.parse(user.mapped_device_ids ?? '[]');
      if (deviceId) {
        if (!mappedDeviceIds.find(id => id === deviceId)) {
          mappedDeviceIds.push(deviceId);
        }
      }

      await this.dbServiceObj.updateWithoutMapper({ 'last_login': lastLogin, "os_version": loginDto.os_version, "last_login_app_version":loginDto.last_login_app_version, mapped_device_ids: JSON.stringify(mappedDeviceIds) }, 'id', userId, this.userRepository);
      await this.dbServiceObj.saveWithOutMapper( { "event": Constants.USER_LOGIN, "email_id": loginDto.email_id, "zip_code": loginDto.zip_code,  "os_version": loginDto.os_version, "ui_version": loginDto.ui_version, "last_login_app_version":loginDto.last_login_app_version, device_id: deviceId }, userId, this.userLogger);
    } else {
      return { "error_message": "User not found" };
    }
    return response;
  }

  async findOne(id) {
    let response = null
    let user = await this.dbServiceObj.findOne(this.userRepository, 'id', id);
    if(user !== undefined) {
      response = user
    } else {
      user = null;
    }
		return response;
  }

  async savePricingFeedback(userId, pricingFeedbackDto: PricingFeedbackDto) {
    let response = null;
    let user = await this.findOne(userId);
    if(user == undefined) return { "error_message": "User not found" };
    pricingFeedbackDto.user_type = user.type;
    let feedback = await this.dbServiceObj.saveOrUpdateByMultipleWhere(pricingFeedbackDto, this.pricingFeedback, {'product_id':pricingFeedbackDto.product_id, 'price_feedback_type': pricingFeedbackDto.price_feedback_type, 'user_type': user.type, 'user_id': userId});
    if(feedback) {
      response = "Feedback noted";
    }
    return response;
  }

  async saveShareWidgetRequest (userId, shareWidetRequestDto: ShareWidgetRequest) {
    let response = null;
    let user = await this.findOne(userId);
    if(user == undefined) return { "error_message": "User not found" };
    if(shareWidetRequestDto.to_email == shareWidetRequestDto.from_email) return { "error_message": "To and from email cannot be same" };
    let shareWidgetRequest = await this.dbServiceObj.saveWithOutMapper(shareWidetRequestDto, shareWidetRequestDto.user_id,this.shareWidgetRequest);
    if(shareWidgetRequest) {
      await this.awsQueue.sendShareWidgetEmail(shareWidgetRequest);
      response = 'Invitation sent successfully'
    }
    return response;
  }

  async logout(userId, loginDto: UserLoginDto) {
    let response = null;
    let user = await this.dbServiceObj.findOne(this.userRepository, 'id', userId);

    if(user !== undefined) {
      let logoutResponse = await this.dbServiceObj.saveWithOutMapper( { "email_id": user.email_id, "event": Constants.USER_LOGOUT,  "os_version": loginDto.os_version, "ui_version": loginDto.ui_version, "last_login_app_version":loginDto.last_login_app_version  }, user.id, this.userLogger);
      if(logoutResponse)
      {
        response = "Logged out successfully!";
      }
    }
    return response;
  }

  async shareProductPricing (userId, shareProductPricingDto: ShareProductPricing) {
    let response = null;
    let user = await this.findOne(userId);
    if(user == undefined) return { "error_message": "User not found" };
    if(shareProductPricingDto.to_email == shareProductPricingDto.from_email) return { "error_message": "To and from email cannot be same" };
    //TODO : Need to modify error messgae
    // if(shareProductPricingDto.price_ft == undefined && shareProductPricingDto.price_lb == undefined) return { "error_message": "No price found" };
    shareProductPricingDto.from_user_type = user.type;
    let shareProductPricing = await this.dbServiceObj.saveWithOutMapper(shareProductPricingDto, shareProductPricingDto.user_id,this.userShareProductPricing);
    if(shareProductPricing) {
      await this.awsQueue.sendShareProductPricing(shareProductPricing);
      response = 'Product prices shared successfully'
    }
    return response;
  }

  async saveAcceptedTermsConditions(userId, saveWidgetTermsConditionDto: WidgetTermsCondtionUserActionsDto) {
    let response = null;

    let user = await this.dbServiceObj.findOne(this.userRepository, 'id', userId);
    if(user) {
      await this.dbServiceObj.saveWithOutMapper(saveWidgetTermsConditionDto, user.id, this.widgetTermsConditionRepository);

      await this.dbServiceObj.updateWithoutMapper({ 'accepted_terms_and_condition': saveWidgetTermsConditionDto.terms_conditions_version}, 'id', user.id, this.userRepository);

      response = 'Saved terms and condition';
    }
    return response;
  }

  
  async getSignedS3Url(userId: string,dto:GetSignedUrl) {
    
   let data = await this.awsUtility.getSignedS3Url(dto.object_key,dto.bucket_name,dto.expire_time);
   return data;
  }

  async saveuploadPoS3Urls(userId, poS3UrlDto: PoS3UrlsDto) {
    let response = null;
    let updateResponse = null;
    //check if PO belongs to user
    let poData = await this.dbServiceObj.findOneByMultipleWhere(this.userPurchaseOrderRepository,{'buyer_po_number':poS3UrlDto.po_number, 'buyer_id' : userId});
    if(poData !== undefined && poData.buyer_id == userId) {
      updateResponse = await this.dbServiceObj.updateByMultipleWhere({'upload_po_s3_urls': poS3UrlDto.upload_po_s3_urls}, {'buyer_po_number': poS3UrlDto.po_number, 'buyer_id': userId}, this.userPurchaseOrderRepository);
    }
    
    if(updateResponse) 
      response = 'Saved successfully';
    else 
      response = null;

    return response;
  }

  async savePORatings(userId,savePoRatingDto: PORatingsDto) {
    let response = null;
    let poOrder = await this.dbServiceObj.findOneByMultipleWhere(this.userPurchaseOrderRepository,{'buyer_po_number':savePoRatingDto.po_number});
    let canRateOrder = false;
    let saveResult = null;
    let user = await this.dbServiceObj.findOne(this.userRepository, 'id', userId);
    if(poOrder !== undefined && user !== undefined) {
      if(savePoRatingDto.user_type == Constants.SELLER) {
        canRateOrder = poOrder.claimed_by == user.email_id;
      } else if(savePoRatingDto.user_type == Constants.BUYER) {
        canRateOrder = poOrder.buyer_id == user.id;
      }
    }

    if(canRateOrder) {
      //deactivate existing rating
      await this.dbServiceObj.markInActiveMultipleWhere(this.userOrderRatingsRepository,{'user_id': userId, 'po_number': savePoRatingDto.po_number, 'user_type': savePoRatingDto.user_type});
      saveResult = await this.dbServiceObj.saveWithOutMapper(savePoRatingDto, userId, this.userOrderRatingsRepository);
    }
    
    if(saveResult)
      response = 'Ratings saved successfully'
    else 
      response = null;
    return response;
  }

  async saveUploadSoS3Urls(userId, poS3UrlDto: PoS3UrlsDto) {
    let response = null;
    let updateResponse = null;
    //check if PO belongs to user
    let poOrder = await this.dbServiceObj.findOneByMultipleWhere(this.userPurchaseOrderRepository,{'buyer_po_number':poS3UrlDto.po_number, 'seller_id' :userId});
    if(poOrder !== undefined ) {
      updateResponse = await this.dbServiceObj.updateByMultipleWhere({'upload_so_s3_urls': poS3UrlDto.upload_po_s3_urls}, {'buyer_po_number': poS3UrlDto.po_number}, this.userPurchaseOrderRepository);
    }
    
    if(updateResponse) 
      response = 'Saved successfully';
    else 
      response = null;

    return response;
  }

  // async generateExcelFile() {

  //     const data = await this.dbServiceObj.findAll(this.userRepository);

  //     let rows = []

  //       data.forEach(doc => {
  //         rows.push(Object.values(doc))
  //       })
        
  //       let book = new Workbook();
        
  //       let sheet = book.addWorksheet(`sheet1`)
        
  //       rows.unshift(Object.keys(data[0]))
        
  //       sheet.addRows(rows)
        
        
        
  //       let file = await new Promise((resolve, reject) => {
  //         tmp.file({ discardDescriptor: true, prefix : `MyExcelSheet`, postfix: '.xlsx', mode: parseInt('0600', 8)}, async(err, file) => {
  //         if(err){
  //           throw new BadRequestException(err);
  //         }

  //         book.xlsx.writeFile(file).then(aa => {
  //           resolve(file)
  //         }).catch(err =>{
  //           throw new BadRequestException(err);
  //         })
  //       })
  //     })
  //    return file;
  // }

  async generatePurchaseOrderExcelFile(userId: string) {

    const data = await this.connection.query("select upol.domestic_material_only, upol.order_status_id as status, DATE_FORMAT(upo.`created_date`,'%m/%d/%y') as 'Date' ,upoled.`transaction_type` as 'Event' ,upo.`payment_method` as 'Method of Payment' ,'BRYZOS' as 'Purchased From', upo.`claimed_by` as 'Fulfilled By' ,DATE_FORMAT(upo.`delivery_date`,'%m/%d/%y') as 'Shipment Date' ,upo.`buyer_po_number` as 'Bryzos SO' ,upo.`buyer_internal_po` as 'Your PO',upol.`po_line`as 'Line',upol.`description` as 'Description',upol.`actual_qty` as 'Qty',upol.`qty_unit` as 'Qty UM',upol.`buyer_price_per_unit` as 'Price',upol.`price_unit` as 'Price UM' ,upol.`buyer_line_total` as 'Extended',IF((upol.`sales_tax`  IS NULL), 0.00 , upol.`sales_tax`) as 'Sales Tax',IF((upoled.`bryzos_fees` IS NULL), 0.00 , upoled.`bryzos_fees`) as 'Bryzos Fees',upol.`actual_buyer_line_total` as 'Original total', upo.`buyer_invoice_name` as 'Invoice',upol.`buyer_price_per_unit` as 'Credit/Debit' , upol.`actual_buyer_line_total` + IF((upol.`sales_tax`  IS NULL), 0.00 , upol.`sales_tax`) + IF((upoled.`bryzos_fees` IS NULL), 0.00 , upoled.`bryzos_fees`) as 'Current Total', upo.`buyer_checkout_pdf_url` as 'URL To Download' from user_purchase_order upo Left Join user_purchase_order_line upol ON(upo.id = upol.purchase_order_id) inner join user_purchase_order_ledger upoled on (upol.id = upoled.purchase_order_line_id) where upo.`buyer_id` = "+userId+"  Group by upoled.purchase_order_line_id ORDER BY upoled.created_date DESC;" );

    if(data.length > 0){
      
      let rows = []
      const activeOrder = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ORDERACTIVE);
      const canceledOrder = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ORDERCANCELLED);
      const closeOrder = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ORDERCOMPLETED);
   
      for(let doc of data)
        {

        if(doc.domestic_material_only == 1) {
          doc.Description = doc.Description + '\n'+Constants.DOMESTIC_MATERIAL_VALUE;
        }
        if(doc.status == activeOrder.id){
          doc.status = Constants.ORDER_STATUS_OPEN;
        }else if(doc.status == canceledOrder.id){
          doc.status = Constants.ORDER_STATUS_CANCELED;
        }else if(doc.status == closeOrder.id){
          doc.status = Constants.ORDER_STATUS_CLOSE;
        }

        let prc = doc.Price; 
     
       let extnd = doc.Extended; 
        let orgTot = doc['Original total']; 
        let currntTot = doc['Current Total'];
        const unit = doc['Price UM'];
        let credDeb = doc['Credit/Debit']; 

        if(unit == Constants.LB)
        {
          prc = await this.truncateNumberFormat(prc,4);
          credDeb = await this.truncateNumberFormat(credDeb,4);
        }
        else 
        {
          prc =  await this.truncateNumberFormat(prc,2);
          credDeb = await this.truncateNumberFormat(credDeb,2); 
        }
        extnd = await this.truncateNumberFormat(extnd,2);
        orgTot = await this.truncateNumberFormat(orgTot,2);
        currntTot = await this.truncateNumberFormat(currntTot,2);

        doc.Price = prc; 
        doc.Extended = extnd; 
        doc['Original total'] = orgTot;
        doc['Current Total'] = currntTot; 
        doc['Credit/Debit'] = credDeb; 
 
        rows.push(Object.values(doc));
        }

      let book = new Workbook();
      let sheet = book.addWorksheet(`sheet1`)

      const headerRow = ['Bryzos Purchase Order History'];
      const headerRowFont = {
        bold: true,
        color: { argb: 'FF000000' }, // black color
        size: 16 // font size in points
      };
      
      sheet.addRow(headerRow);
      sheet.getRow(1).font = headerRowFont;
      rows.unshift(Object.keys(data[0]))
      sheet.addRows(rows)

      let file = await new Promise((resolve, reject) => {
        tmp.file(
          { 
            discardDescriptor: true,
            prefix : '',
            postfix: '',
            dir:'',
            // mode: parseInt('0600', 8),
            unsafeCleanup: true,
            keep: true
          },
          async(err, file) => {
            if(err){
              throw new BadRequestException(err);
            }
            book.xlsx.writeFile(file).then(aa => {
              console.log(file);
              resolve(file)
            }).catch(err =>{
              throw new BadRequestException(err);
            });
          }
        );
      });
      return file;
    }else{
      return {"error_message":"Sorry No Data"}
    }
  }
 
  async generateSalesOrderExcelFile(userId: string) {
    
    let data = await this.connection.query("select upol.domestic_material_only,  upol.order_status_id as status, DATE_FORMAT(upo.`created_date`,'%m/%d/%y') as 'Date' ,upoled.`transaction_type` as 'Event' ,'BRYZOS' as 'Purchaser', user.`email_id` as 'Delivered To' ,DATE_FORMAT(upo.`delivery_date`,'%m/%d/%y') as 'Shipment Date' ,upo.`seller_po_number` as 'Bryzos PO' ,upo.`buyer_internal_po` as 'Your SO',upol.`po_line`as 'Line',upol.`description` as 'Description',upol.`actual_qty` as 'Qty',upol.`qty_unit` as 'Qty UM',upol.`seller_price_per_unit` as 'Price',upol.`price_unit` as 'Price UM' ,upol.`seller_line_total` as 'Extended',IF((upoled.`bryzos_fees` IS NULL), 0.00 , upoled.`bryzos_fees`) as 'Bryzos Fees',upol.`actual_seller_line_total` as 'Original total',upol.`seller_price_per_unit` as 'Credit/Debit' , upol.`actual_seller_line_total` +  IF((upol.`seller_sales_tax`  IS NULL), 0.00 , upol.`seller_sales_tax`) + IF((upoled.`bryzos_fees` IS NULL), 0.00 , upoled.`bryzos_fees`) as 'Current Total', upo.`seller_claimed_pdf_url` as 'URL To Download' from user_purchase_order upo Left Join user_purchase_order_line upol ON(upo.id = upol.purchase_order_id) Left join user_purchase_order_ledger upoled on (upol.id = upoled.purchase_order_line_id) Left join user on (upo.`buyer_id` = user.id)where upo.`seller_id` = "+userId+" and upo.`buyer_id` is not NULL ");

    if(data.length > 0){
      
      let rows = []
      const activeOrder = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ORDERACTIVE);
      const canceledOrder = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ORDERCANCELLED);
      const closeOrder = await this.dbServiceObj.findOne(this.userReferenceDataOrderStatus,'value',Constants.ORDERCOMPLETED);

      for(let doc of data)
      {
        if(doc.domestic_material_only == 1) {
          doc.Description = doc.Description + '\n'+Constants.DOMESTIC_MATERIAL_VALUE;
        }
        if(doc.status == activeOrder.id){
          doc.status = Constants.ORDER_STATUS_OPEN;
        }else if(doc.status == canceledOrder.id){
          doc.status = Constants.ORDER_STATUS_CANCELED;
        }else if(doc.status == closeOrder.id){
          doc.status = Constants.ORDER_STATUS_CLOSE;
        }

        let prc = doc.Price; 
        let extnd = doc.Extended; 
         let orgTot = doc['Original total']; 
         let currntTot = doc['Current Total'];
         const unit = doc['Price UM'];
         let credDeb = doc['Credit/Debit'];
 
         if(unit == Constants.LB)
         {
           prc = await this.truncateNumberFormat(prc,4);
           credDeb= await this.truncateNumberFormat(credDeb,4);
         }
         else 
         {
           prc =  await this.truncateNumberFormat(prc,2);
           credDeb= await this.truncateNumberFormat(credDeb,2);
         }
 
         extnd = await this.truncateNumberFormat(extnd,2);
         orgTot = await this.truncateNumberFormat(orgTot,2);
         currntTot = await this.truncateNumberFormat(currntTot,2);
 
         doc.Price = prc; 
         doc.Extended = extnd; 
         doc['Original total'] = orgTot;
         doc['Current Total'] = currntTot; 
         doc['Credit/Debit'] = credDeb;

        rows.push(Object.values(doc));
      }
      
      let book = new Workbook();
      let sheet = book.addWorksheet(`sheet1`)

      const headerRow = ['Bryzos Sales Order History'];
      const headerRowFont = {
        bold: true,
        color: { argb: 'FF000000' }, // black color
        size: 16 // font size in points
      };
      
      sheet.addRow(headerRow);
      sheet.getRow(1).font = headerRowFont;
      rows.unshift(Object.keys(data[0]))
      sheet.addRows(rows)


      let file = await new Promise((resolve, reject) => {
        tmp.file(
          { 
            discardDescriptor: true,
            prefix : '',
            postfix: '',
            dir:'',
            // mode: parseInt('0600', 8),
            unsafeCleanup: true,
            keep: true
          },
          async(err, file) => {
            if(err){
              throw new BadRequestException(err);
            }
            book.xlsx.writeFile(file).then(aa => {
              console.log(file);
              resolve(file)
            }).catch(err =>{
              throw new BadRequestException(err);
            });
          }
        );
      });
      return file;
    }else{
      return {"error_message":"Sorry No Data"}
    }
  }

  async generatePayableStatementExcelFile(userId: string) {
    const data = await this.connection.query("select DATE_FORMAT(upo.`created_date`,'%m/%d/%y') as 'Date of Purchase' , upo.`buyer_po_number` as 'Your PO', upo.`buyer_internal_po` as 'Bryzos SO',upo.`payment_method` as 'Method of Payment' ,DATE_FORMAT(upo.`delivery_date`,'%m/%d/%y') as 'Delivery Date' , upol.`price_unit` as 'Price UM', upol.`buyer_price_per_unit` as 'Material Price',IF((upol.`sales_tax`  IS NULL), 0.00 , upol.`sales_tax`) as 'Sales Tax',upol.`actual_buyer_line_total` as 'Final total',IF((upoled.`buyer_paid`  IS NULL), 0.00 , upoled.`buyer_paid`) as `Total Paid` ,IF(( upoled.`buyer_purchase`  IS NULL), 0.00 ,  upoled.`buyer_purchase`) - IF((upoled.`buyer_paid`  IS NULL), 0.00 , upoled.`buyer_paid`) as 'Current Balance' from user_purchase_order upo Left Join user_purchase_order_line upol ON(upo.id = upol.purchase_order_id) Left join user_purchase_order_ledger upoled on (upol.id = upoled.purchase_order_line_id) where upo.`buyer_id` = "+userId+" and upo.`is_active` = 1 and upol.`is_active` = 1 and upoled.`is_active` = 1 Group by upoled.purchase_order_line_id ORDER BY upoled.created_date DESC;");

    if(data.length > 0){
      
      let rows = []

      for(let doc of data)
      {   
        const unit = doc['Price UM'];

        let prc = doc['Material Price'];
        let finalTot = doc['Final total'];

        if(unit == Constants.LB)
        {
          prc = await this.truncateNumberFormat(prc,4);
        }
        else 
        {
          prc =  await this.truncateNumberFormat(prc,2);
        }

        finalTot = await this.truncateNumberFormat(finalTot,2); 

        doc['Material Price'] = prc;
        doc['Final total'] = finalTot;

        rows.push(Object.values(doc));
      }
  
      let book = new Workbook();
      let sheet = book.addWorksheet(`sheet1`)

      const headerRow = ['Bryzos Accounts Payable Statement'];
      const headerRowFont = {
        bold: true,
        color: { argb: 'FF000000' }, // black color
        size: 16 // font size in points
      };
      
      sheet.addRow(headerRow);
      sheet.getRow(1).font = headerRowFont;
      rows.unshift(Object.keys(data[0]))
      sheet.addRows(rows)
    

      let file = await new Promise((resolve, reject) => {
        tmp.file(
          { 
            discardDescriptor: true,
            prefix : '',
            postfix: '',
            dir:'',
            // mode: parseInt('0600', 8),
            unsafeCleanup: true,
            keep: true
          },
          async(err, file) => {
            if(err){
              throw new BadRequestException(err);
            }
            book.xlsx.writeFile(file).then(aa => {
              console.log(file);
              resolve(file)
            }).catch(err =>{
              throw new BadRequestException(err);
            });
          }
        );
      });
      return file;
    }else{
      return {"error_message":"Sorry No Data"}
    }
  }

  async generateReceivableStatementExcelFile(userId: string) {
    const data = await this.connection.query("select DATE_FORMAT(upo.`created_date`,'%m/%d/%y') as 'Date of Sale' , upo.`seller_po_number` as 'Bryzos PO', upo.`buyer_internal_po` as 'Your SO',upol.`actual_seller_line_total` + IF((upol.`seller_sales_tax`  IS NULL), 0.00 , upol.`seller_sales_tax`) as 'Final Order total' from user_purchase_order upo Left Join user_purchase_order_line upol ON(upo.id = upol.purchase_order_id) Left join user_purchase_order_ledger upoled on (upol.id = upoled.purchase_order_line_id) where upo.`seller_id` = "+userId+" and upo.`buyer_id` is not NULL and upo.`is_active` = 1 and upol.`is_active` = 1 and upoled.`is_active` = 1;");

    if(data.length > 0){
      
      let rows = []
    
      for( let doc of data)
      {

        let ordTot =  doc['Final Order total'];
        ordTot = await this.truncateNumberFormat(ordTot,2); 
        doc['Final Order total'] = ordTot;

        rows.push(Object.values(doc));
      }
     
      let book = new Workbook();
      let sheet = book.addWorksheet(`sheet1`)

      const headerRow = ['Bryzos Accounts Receivable Statement'];
      const headerRowFont = {
        bold: true,
        color: { argb: 'FF000000' }, // black color
        size: 16 // font size in points
      };
      
      sheet.addRow(headerRow);
      sheet.getRow(1).font = headerRowFont;
      rows.unshift(Object.keys(data[0]))
      sheet.addRows(rows)

      let file = await new Promise((resolve, reject) => {
        tmp.file(
          { 
            discardDescriptor: true,
            prefix : '',
            postfix: '',
            dir:'',
            // mode: parseInt('0600', 8),
            unsafeCleanup: true,
            keep: true
          },
          async(err, file) => {
            if(err){
              throw new BadRequestException(err);
            }
            book.xlsx.writeFile(file).then(aa => {
              console.log(file);
              resolve(file)
            }).catch(err =>{
              throw new BadRequestException(err);
            });
          }
        );
      });
      return file;
    }else{
      return {"error_message":"Sorry No Data"}
    }
  }

  async getResaleCertByPONumber(po_number: string){
    const order = await this.dbServiceObj.findOneByMultipleWhere(this.userPurchaseOrderRepository,{'buyer_po_number':po_number,'is_active':true});
    if(order){
      const internal_po_number = order.buyer_internal_po;
      const buyer_id = order.buyer_id; 

      const resale_order = await this.dbServiceObj.findAllByUserId(this.userResaleCertificateRepository,buyer_id);

      let certificate_url = [];
      if(resale_order.length > 0){
        resale_order.forEach(value=>{
          if(value.is_active){
            certificate_url.push(value.cerificate_url_s3);
          }
        })
      }
      const finall_data = {
        "buyer_internal_po":internal_po_number,
        "resale_certificate_url":certificate_url
      }
      return finall_data;
    }else{
      return {'error_message':'Sorry there is no such PO exist'};
    }
  }

  async getUserProductTagMapping(userId) {
    let response = null;
    let productTags = await this.dbServiceObj.findMany(this.userProductTagMappingRepository,'user_id',userId);
    if(productTags.length > 0) {
      var finallData = {};
      for(let product of productTags) {
        let productId = product.product_id;
        let tag = product.tag;
        finallData[productId]=tag;
      }
      response = finallData;
    }
    return response;
  }

  async validateStateZip(payload){
    
    const state_id = payload.state_id;
    const zipcode = payload.zip_code;

    const state_data = await this.dbServiceObj.findOneByMultipleWhere(this.referenceDataSalesTaxRepository,{state_id:state_id,zipcode:zipcode});

    if(state_data){
      return true;
    }else{
      return {"error_message":"The zip code and state code do not match."};

    }
  }
  
  async saveCreatePoOpenClose(payload,userId){
    let response = null;
    const date = new Date();
    const datetime = date.toISOString();

    const checkSession = await this.dbServiceObj.findOne(this.userCreatepoOpenClose,"session_id",payload.session_id);
    if(checkSession){
      if(payload.close_status){
        payload.close_time = datetime;
      }
      if(payload.shipping_details){
        let shippingDetails = payload.shipping_details;
        payload.line1 = shippingDetails.line1 ? shippingDetails.line1 : null;
        payload.city = shippingDetails.city ? shippingDetails.city : null;
        payload.zip = shippingDetails.zip ? shippingDetails.zip : null;
        payload.state_id = shippingDetails.state_id ? shippingDetails.state_id : null;
      }
      delete(payload.shipping_details);
      const update = await this.dbServiceObj.updateByMultipleWhere(payload,{"session_id":payload.session_id},this.userCreatepoOpenClose);

      if(payload.po_number){
        await this.dbServiceObj.updateByMultipleWhere({"po_number":payload.po_number},{"session_id":payload.session_id},this.userSearchAnalytics);
        const poLines = await this.dbServiceObj.findMany(this.userPurchaseOrderLineRepository,"buyer_po_number",payload.po_number);
        if(poLines){
          for(const poLine of poLines){
            const sellerPoNumber = payload.po_number.replace('S','P');
            const updateValues = {
              "buyer_po_number":payload.po_number,
              "quote_number":poLine.quote_number,
              "seller_po_number":sellerPoNumber,
              "buyer_calculation_price":poLine.buyer_calculation_price_per_unit,
              "seller_price_per_unit":poLine.seller_price_per_unit,
              "seller_extended":poLine.actual_seller_line_total,
              "seller_calculation_price":poLine.seller_calculation_price_per_unit,
            };
            await this.dbServiceObj.updateByMultipleWhere(updateValues,{"session_id":payload.session_id,"in_po_line":true,"po_line":poLine.po_line},this.userSearchLineDetailsRepository);
          }
        }
      }

      if(update){
        response = "Updated Successfully!";
      }
    }else{
      payload.open_time = datetime
      const save = await this.dbServiceObj.saveWithOutMapper(payload,userId,this.userCreatepoOpenClose);
      if(save){
        response = "Saved Successfully!";
      }
    }
    return response;
  }

  async saveCreatePoSearch(payload,userId){
    let response = null;
    const saveSearch = await this.dbServiceObj.saveWithOutMapper(payload,userId,this.userSearchAnalytics);
    if(saveSearch){
      response = "Saved Successfully!";
    }
    return response;
  }

  async saveOnBoardUserRequest(payload:OnBoardDto){
    let response = null;
    const onBoardPendinguserCheck = await this.dbServiceObj.findOne(this.userOnboradPendingRequestsRepository,'email_id',payload.email_id);
    const userCheck = await this.dbServiceObj.findOne(this.userRepository,'email_id',payload.email_id);
    if(onBoardPendinguserCheck || userCheck){
      return {"error_message":"This email-id already exist"};
    }

    const validateZip = await this.checkZipCodeExist(payload.zip_code);
    if(validateZip.hasOwnProperty("error_message")){
      return validateZip;
    }
    
    const company_name= payload.company_name.replace(/<[^>]*>/g, '').trim();
    const companyId = await this.checkCompanyExist(company_name);
    payload['company_name']=company_name;
    if(companyId){
      payload['company_id']=companyId;
    }else{
      const companyName = {
        'company_name': company_name
      }

      const insertNewCompany = await this.dbServiceObj.saveData(companyName,this.userPendingCompanyRequestsRepository);
    }

    const insert = await this.dbServiceObj.saveData(payload,this.userOnboradPendingRequestsRepository);
    if(insert){
      const referenceId = insert.id;
      const event = Constants.USER_ONBOARD_PENDING_REQUEST;
      let  messageBody= "Send user onboarding request";
      await this.awsQueue.sendDataToAwsQueue(referenceId,event,messageBody);
      response  = "Your request has been forwarded. Please wait for a response.";
    }else{
      response = {"error_message":"Something went wrong!, Please contact support"};
    }
    return response
  }

  async checkUserEmailAlreadyExist(payload:UserEmailDto){
    let response = null;
    const onBoardPendinguserCheck = await this.dbServiceObj.findOne(this.userOnboradPendingRequestsRepository,'email_id',payload.email_id);
    const userCheck = await this.dbServiceObj.findOne(this.userRepository,'email_id',payload.email_id);
    if(onBoardPendinguserCheck || userCheck){
      return {"error_message":"This email-id already exist"};
    }

    response  = "Email does not exist. You can proceed.";
    
    return response;
  }

  async getCassAccessToken(userId) {
    let response = null;
    const axios = require('axios');
    //TODO constant in base library
    let cassPassword =  await this.baseLibraryService.getSecretValue(process.env.SM_ENV,Constants.VENDOR_CASS, Constants.PASSWORD);
    if(cassPassword) {
      const postBody = {
        userName: process.env.CASS_USER,
        password: cassPassword,
      };

      const headers = {
        'Content-Type': 'application/json',
      };

      try {
        const cassCurlResponse = await axios.post(process.env.CASS_URL, postBody, { headers });
        const jsonParsed = cassCurlResponse.data;
        response = jsonParsed?.token ?? null;
      } catch (error) {
        BryzosLogger.log(JSON.stringify(error.response.data), process.env.CASS_LOGGLY_TAG);
        return { "error_message": "Something went wrong" };
      }
    }
    return response;
  }

  async saveTooltip(payload:TooltipDto)
  {
    
    let tooltipResult = null;
    try{

      const updateTooltipDto = { tooltips : payload.tooltips };

      tooltipResult = await this.dbServiceObj.updateByColumnId( this.userRepository, updateTooltipDto, 'id', payload.user_id);
        
      if(!tooltipResult)
      {
        return { "error_message": "Something went wrong! cannot save tooltip!" };
      }
      else 
      {
        tooltipResult = "Tooltips saved.";
      }

    }
    catch(error)
    {
      return { "error_message": "Something went wrong! cannot save tooltip!" };
    }
        delete payload.user_id;

       return payload; 
  }

  async getTooltip(userId)
  {
    let response = undefined;
    try {

      response = await this.dbServiceObj.findOne(
        this.userRepository,
        'id',
        userId,
      );

      if (!response) {
        return {
          "error_message": ' Sorry unable to fetch data from database. !',
        };
      }
     
    } catch (error) {
      console.log('Sorry unable to fetch data!!');
      response = {
        "error_message": ' Sorry unable to fetch data from database!',
      };
      return response;
    }
    return {tooltips:JSON.parse(response.tooltips)};
  }


  async deleteResaleCertificate(certId)
  {
    //fetch the certificate data from table 
    let certData = await this.dbServiceObj.findOne(this.userResaleCertificateRepository,'id',certId);

    if(!certData)
    return {"error_message":"Something went wrong!"}

    if(certData.status != Constants.Approved )
    {
      return {"error_message":"Certificate is not approved!"}
    }

    if(certData.is_deletable == false ){
        return {"error_message":"You cannot delete certificate uploaded by company"}
    }
   
    const updateDelCertDto = {
      is_active : 0
    }

    const isDeleted =  await this.dbServiceObj.updateByColumnId( this.userResaleCertificateRepository, updateDelCertDto, 'id', certId);

    if(!isDeleted)
      return {"error_message":"Unable to delete certificate!"};

      // send certificate id to aws sqs and trigger the email
      const buyerId = certData.user_id;

      await this.awsQueue.sendDeleteResaleCertAlert(certId);
      await this.awsQueue.sendDeleteResaleCertNotification(buyerId);

      return "Certificate deleted successfully!";
  }
  
  async savePoLineDetails(payload,userId){
    const currentDateTime = new Date();
    const foramtcurrentDateTime = await Utility.getCtDateTime(currentDateTime,'M/d/yy hh:mm a');

    let response = null;
    const uploadData = {};
    uploadData["session_id"] = payload.session_id ? payload.session_id : null;
    uploadData["po_line"] = payload.po_line ? payload.po_line : null;
    uploadData["line_session_id"] = payload.line_session_id ? payload.line_session_id : null;
    uploadData["in_po_line"] = payload.in_po_line ? payload.in_po_line : false;
    uploadData["description"] = payload.description ? payload.description : null;
    uploadData["qty"] = payload.qty ? payload.qty : null;
    uploadData["qty_unit"] = payload.qty_unit ? payload.qty_unit : null;
    uploadData["product_tag"] = payload.product_tag ? payload.product_tag : null;
    uploadData["price_unit"] = payload.price_unit ? payload.price_unit : null;
    uploadData["buyer_price_per_unit"] = payload.price ? payload.price : null;
    uploadData["buyer_extended"] = payload.extended ? payload.extended : null;
    uploadData["product_id"] = payload.product_id ? payload.product_id : null;
    uploadData["reference_product_id"] = payload.reference_product_id ? payload.reference_product_id : null;
    uploadData["shape"] = payload.shape ? payload.shape : null;
    uploadData["price_unit"] = payload.price_unit ? payload.price_unit : null;
    uploadData["domestic_material_only"] = payload.domestic_material_only ? payload.domestic_material_only : null;
    uploadData["user_id"] = userId;

    if(payload.product_id){
      const unitPrice = await this.dbServiceObj.findOne(this.referenceDataSettingsRepository,"name",Constants.USER_SEARCH_LINE_DETAILS_QTY);
      const referenceProductData = await this.dbServiceObj.findOne(this.referenceDataProductsWidgetRepository,"Product_ID",payload.product_id);
      if(unitPrice && referenceProductData){
        const qtyUnit = unitPrice.value;
        uploadData["neutral_pricing"] = referenceProductData["Neutral_Pricing_"+qtyUnit].replace(/[$&,]/g, '');
        uploadData["buyer_pricing"] = referenceProductData["Buyer_Pricing_"+qtyUnit].replace(/[$&,]/g, '');
        uploadData["seller_pricing"] = referenceProductData["Seller_Pricing_"+qtyUnit].replace(/[$&,]/g, '');
        uploadData["unit_price"] = qtyUnit;

        let buyer_delta_percent = ((Number(uploadData["buyer_pricing"]) - Number(uploadData["neutral_pricing"]) ) / Number(uploadData["neutral_pricing"]) ) * 100;
        let seller_delta_percent = ((Number(uploadData["seller_pricing"]) - Number(uploadData["neutral_pricing"]) ) / Number(uploadData["neutral_pricing"]) ) * 100;

        uploadData["buyer_delta_percent"] = parseFloat(buyer_delta_percent.toFixed(2));
        uploadData["seller_delta_percent"] = parseFloat(seller_delta_percent.toFixed(2));

      }
    }

    const checkLineSession = await this.dbServiceObj.findOneByMultipleWhere(this.userSearchLineDetailsRepository,{"session_id":payload.session_id,"line_session_id":payload.line_session_id});

    if(checkLineSession){
      uploadData["time_out"] = foramtcurrentDateTime;
      const update = await this.dbServiceObj.updateByMultipleWhere(uploadData,{"session_id":payload.session_id,"line_session_id":payload.line_session_id},this.userSearchLineDetailsRepository);
      if(!uploadData["in_po_line"]){
        await this.syncPoLines(payload.session_id,payload.po_line);
      }
      if(update){
        response = "Updated Successfully";
      }
    }else{
      uploadData["time_in"] = foramtcurrentDateTime;
      const save = await this.dbServiceObj.saveData(uploadData,this.userSearchLineDetailsRepository);
      if(save){
        response = "Saved Successfully";
      }
    }
    return response;
  }

  async checkZipCodeExist(zipCode){
    let response = null;
    const checkZipCode = await this.dbServiceObj.findOne(this.referenceDataStateZipcodeRepository,"zip_code",zipCode);
    if(!checkZipCode){
      response = {"error_message":"Invalid Zipcode"};
    }else{
      response = true;
    }
    return response;
  }

  async syncPoLines(sessionId,poLineNo){
    const poLines = await this.dbServiceObj.findMany(this.userSearchLineDetailsRepository,"session_id",sessionId);
    for(const poLine of poLines){
      if(Number(poLine.po_line) > Number(poLineNo)){
        let newPoLineNo = Number(poLine.po_line) - 1;
        await this.dbServiceObj.updateByMultipleWhere({"po_line":newPoLineNo},{"session_id":sessionId,"line_session_id":poLine.line_session_id},this.userSearchLineDetailsRepository);
      }
    }
  }

 /* async truncateDecimal(number: number, decimalPlaces: number): number {
    return Number(number.toFixed(decimalPlaces));
  }*/

   async truncateNumberFormat(amt,afterDecimalCount = 2){
    const decimalPosition: number = amt.indexOf('.') + 1;
    const digitLimit: number = decimalPosition + afterDecimalCount;
    const finalNumber: string = amt.substring(0, digitLimit);
    let numberFormat: string = Number(finalNumber).toLocaleString('en-US', {
      minimumFractionDigits: afterDecimalCount,
      maximumFractionDigits: afterDecimalCount
    });
    return numberFormat;
  }
  
  async getMainCompanyList() {

    let response = [];

    let userCompanyClientLocations = await this.dbServiceObj.selectCustomFields(this.userRepository,"is_active","1",["distinct(client_company) as clientCompany","company_id as companyId"]);
    const clientCompanyId = {};
    for(const userCompanyClientLocation of userCompanyClientLocations){
      if(userCompanyClientLocation.clientCompany && userCompanyClientLocation.companyId){
        if(clientCompanyId.hasOwnProperty(userCompanyClientLocation.companyId)){
          clientCompanyId[userCompanyClientLocation.companyId].push(userCompanyClientLocation.clientCompany);
        }else{
          clientCompanyId[userCompanyClientLocation.companyId]=[];
          clientCompanyId[userCompanyClientLocation.companyId].push(userCompanyClientLocation.clientCompany);
        }
      }
    }
    const companies = await this.dbServiceObj.selectCustomFieldsOrderByCreatedDate(this.userMainCompanyRepository, "is_active", "1", "*", "DESC");
    if(companies.length > 0) {
      for(let company of companies){
        let clientCompany=[]; 
        if(clientCompanyId.hasOwnProperty(company.id)){
            clientCompany=clientCompanyId[company.id];
        }
        const data = {
          'id': company.id,
          'company_name':company.company_name,
          'client_company':clientCompany,
          'disc_discount_period':company.deprecated_disc_discount_period,
          'disc_discount_phaseout_startdate':company.deprecated_disc_discount_phaseout_startdate,
          'disc_discount_phaseout_period':company.deprecated_disc_discount_phaseout_period,
          'disc_discount_percentage':company.buyer_spread_percentage,
          'disc_discount_pricing_column':company.base_pricing_column,
          'seller_spread_rate':company.seller_spread_rate,
        };
        
        response.push(data);
      }
      return response;
    }else{
        return {'error_message':'No company found!'};
    }    
  }

  async checkCompanyExist(companyName){
    let response = null;
    const checkCompanyName = await this.dbServiceObj.findOne(this.userMainCompanyRepository,"company_name",companyName);
    if(checkCompanyName){
      response = checkCompanyName.id;
    }
    return response;
  }

  async saveSearchPriceData(payloads,userId){
    let response = null;

    try{
      if(payloads && userId){
        for(let payload of payloads){
          payload["user_id"] = userId;
          let priceJson = {};
          if(payload.hasOwnProperty("product_id")){
            const productData = await this.dbServiceObj.findOne(this.referenceDataProductsWidgetRepository,"Product_ID",payload["product_id"])
            if(productData){
                payload['Neutral_Pricing_Ft']=Number(productData.Neutral_Pricing_Ft.replace(/[$&,]/g, ''));
                payload['Neutral_Pricing_Ea']=Number(productData.Neutral_Pricing_Ea.replace(/[$&,]/g, ''));
                payload['Neutral_Pricing_LB']=Number(productData.Neutral_Pricing_LB.replace(/[$&,]/g, ''));
                payload['Neutral_Pricing_CWT']=Number(productData.Neutral_Pricing_CWT.replace(/[$&,]/g, ''));
                payload['Neutral_Pricing_Net_Ton']=Number(productData.Neutral_Pricing_Net_Ton.replace(/[$&,]/g, ''));
                payload['Buyer_Pricing_Ft']=Number(productData.Buyer_Pricing_Ft.replace(/[$&,]/g, ''));
                payload['Buyer_Pricing_Ea']=Number(productData.Buyer_Pricing_Ea.replace(/[$&,]/g, ''));
                payload['Buyer_Pricing_LB']=Number(productData.Buyer_Pricing_LB.replace(/[$&,]/g, ''));
                payload['Buyer_Pricing_CWT']=Number(productData.Buyer_Pricing_CWT.replace(/[$&,]/g, ''));
                payload['Buyer_Pricing_Net_Ton']=Number(productData.Buyer_Pricing_Net_Ton.replace(/[$&,]/g, ''));
                payload['Seller_Pricing_Ft']=Number(productData.Seller_Pricing_Ft.replace(/[$&,]/g, ''));
                payload['Seller_Pricing_Ea']=Number(productData.Seller_Pricing_Ea.replace(/[$&,]/g, ''));
                payload['Seller_Pricing_LB']=Number(productData.Seller_Pricing_LB.replace(/[$&,]/g, ''));
                payload['Seller_Pricing_CWT']=Number(productData.Seller_Pricing_CWT.replace(/[$&,]/g, ''));
                payload['Seller_Pricing_Net_Ton']=Number(productData.Seller_Pricing_Net_Ton.replace(/[$&,]/g, ''));
                payload['domestic_material_only']=productData.domestic_material_only == 1 ? true : false;
            }
          }

          const whereConditions = {
            "session_id":payload["session_id"],
            "line_session_id":payload["line_session_id"],
            "user_id":userId
          }

          let checkUserSession = await this.dbServiceObj.findOneByMultipleWhere(this.userSearchPriceDetailsRepository,whereConditions);
          if(checkUserSession){
            await this.dbServiceObj.updateByMultipleWhere(payload,whereConditions,this.userSearchPriceDetailsRepository);
          }else{
            const currentDateTime = new Date();
            const foramtcurrentDateTime = await Utility.getCtDateTime(currentDateTime,'M/d/yy hh:mm a');
            payload["search_time"]=foramtcurrentDateTime;
            await this.dbServiceObj.saveData(payload,this.userSearchPriceDetailsRepository);
          }
        }
      }
      response = "Saved Successfully!";
    }catch(er){
      response = "Something went wrong!";
    }
    return response;
  }

  async saveSearchPriceMoveOutScreen(payload,userId){
    payload["user_id"]=userId;
    await this.dbServiceObj.saveData(payload,this.userSearchPriceScreenMoveOutRepository);
    return "Saved SuccessFully!";
  }

  async updateUser(updatePayload, userId)
  {
    await this.dbServiceObj.updateWithoutMapper(updatePayload, 'id', userId, this.userRepository);
  }

  async deleteUser(userId)
  {
    let userData = await this.dbServiceObj.findOne(this.userRepository,'id',userId);

    if(!userData){
      await this.dbServiceObj.saveData({user_id: userId, status: 'DELETE_EXCEPTION'}, this.userDeleteAccountRepository);
      return {"error_message":"User not found!"}
    }
  
    const updateDelUserDto = {
      is_active : 0
    }

    const isDeleted = await this.dbServiceObj.updateByColumnId( this.userRepository, {"is_active" : 0, "status": Constants.USER_DELETED}, 'id', userId);

    if(!isDeleted){
      await this.dbServiceObj.saveData({user_id: userId, status: 'DELETE_EXCEPTION'}, this.userDeleteAccountRepository);
      return {"error_message":"Unable to delete user!"};
    }


    if(userData.type === Constants.BUYER){
      await this.dbServiceObj.updateByColumnId( this.userBuyingPreferenceRepository, updateDelUserDto, 'user_id', userId);
      await this.dbServiceObj.updateByColumnId( this.companyBuyNowPayLaterRepository, updateDelUserDto, 'user_id', userId);
      await this.dbServiceObj.updateByColumnId( this.userResaleCertificateRepository, updateDelUserDto, 'user_id', userId);
    }
    
    if(userData.type === Constants.SELLER){
      await this.dbServiceObj.updateByColumnId( this.userSellingPreferenceRepository, updateDelUserDto, 'user_id', userId);
      await this.dbServiceObj.updateByColumnId( this.userArPaymentInfoRepository, updateDelUserDto, 'user_id', userId);
    }
    await this.dbServiceObj.updateByColumnId( this.paymentInfoRepository, updateDelUserDto, 'user_id', userId);
      
    await this.dbServiceObj.saveData({user_id: userId, status: 'DELETED'}, this.userDeleteAccountRepository);


    await this.awsQueue.globalSignOutUser(userData);

    
    return "User deleted successfully!";

  }

  async createPdf(pdfData) {
    if (!pdfData?.data || !pdfData?.file_name) {
      return { "error_message": "invalid request object" };
    }
    try {
      let response = await this.pdfMakerService.makePdf(pdfData, pdfData.file_name);
      return response;
    } catch (err) {
      return { "error_message": err?.message || "sorry, unable to create pdf." };
    }
  }

  async userSpreadData(userId:string){
    let response = null;
    let user = await this.dbServiceObj.findOne(this.userRepository, 'id', userId);
    if(user){
      response = {
        is_discount: user.is_buyer_spread,
        discount_rate: user.buyer_spread_rate,
        discount_pricing_column: user.base_pricing_column,
        discount_period: user.deprecated_disc_discount_period,
        discount_phaseout_startdate: user.deprecated_disc_discount_phaseout_startdate,
        discount_phaseout_period: user.deprecated_disc_discount_phaseout_period,
        is_discount_var_overriden: user.is_buyer_spread_overriden,
      }
    }
    return response;
  }


  /* async createRoom(payload: ChatRoomDto) {
    try {
      const response = (await axios.post(`${this.baseUrl}/consumer/api/v1/chatroom`, payload, { params: this.params }))?.data;
      return response;
    } catch (e) {
      console.log(e.response.data.error.details);
    }
  } */

  async createUser(userId: number) {
    const userData = await this.dbServiceObj.findOne(this.userRepository, "id", userId.toString());
    if (!userData) {
      return { "error_message": "user not found" };
    }

    const userIdentifier = `${process.env.DEAD_SIMPLE_CHAT_UNIQUE_ID_PREFIX}${userId.toString()}`;
    try {
      // check user already exist or not

      const response = (await axios.get(`${this.baseUrl}/consumer/api/v2/user/${userIdentifier}`, { params: this.params })).data;
      return response.uniqueUserIdentifier;

    } catch (error) {
      // user not exist, so create new user

      const userName = `${userData.first_name}_${userData.last_name}`;
      const payload = {
        uniqueUserIdentifier: userIdentifier,
        username: userName,
        firstName: userData.first_name,
        lastName: userData.last_name,
        email: userData.email_id,
        membershipDetails: { roleName: "user", roomId: this.roomId }
      };

      try {
        const response = (await axios.post(`${this.baseUrl}/consumer/api/v2/user`, payload, { params: this.params })).data;
        if (!response) {
          return { "error_message": "Sorry unable to create user" }
        }

        const uniqueUserIdentifier = response.user.uniqueUserIdentifier;
        const saveDto = {
          access_token: response.accessToken,
          id: response.user._id,
          unique_user_identifier: uniqueUserIdentifier,
          user_name: response.user.username,
          email: response.user.email,
          is_moderator: response.membershipDetails.roleName === "moderator",
          room_id: response.membershipDetails.roomId,
          external_user_id: userId.toString(),
        };

        await this.dbServiceObj.saveData(saveDto, this.chatUsersRepository);
        await this.dbServiceObj.updateByColumnId(this.userRepository, { chat_unique_user_identifier: uniqueUserIdentifier }, "id", userId);

        return uniqueUserIdentifier;

      } catch (error) {
        const errorObj = error?.response?.data?.error;

        BryzosLogger.log(JSON.stringify({ error: errorObj, event: "create user in DeadSimple chat error" }), process.env.LOGGLY_REQUEST_RESPONSE_TAG);
        return { "error_message": "Sorry unable to create user in DeadSimple chat" };
      }
    }
  }

  /* async getListOfChannels(roomId: string) {
    try {
      const response = (await axios.get(`${this.baseUrl}/consumer/api/v1/chatroom/${roomId}/channels`, { params: this.params }))?.data;
      return response;
    } catch (e) {
      console.log(e);
    }
  }

  async createChannelAddUsers(data: ChannelAddUsersDto) {
    try {
      let response = null;

      const buyer = await this.dbServiceObj.findOne(this.userRepository, "id", data.buyer_id);
      if (!buyer) {
        return { "error_message": "user not found" };
      }
      const buyerDto = {
        chatRoom: this.roomId,
        externalUserId: buyer.id?.toString(),
        isModerator: false,
        email: buyer.email_id,
        username: `buyer_${buyer.first_name}_${buyer.last_name}`,
      };
      const buyerData = await this.createUserIfNotExist(buyerDto, buyer.chat_unique_user_identifier);

      const seller = await this.dbServiceObj.findOne(this.userRepository, "id", data.seller_id);
      if (!seller) {
        return { "error_message": "user not found" };
      }
      const sellerDto = {
        chatRoom: this.roomId,
        externalUserId: seller.id?.toString(),
        isModerator: false,
        email: seller.email_id,
        username: `seller_${seller.first_name}_${seller.last_name}`,
      };
      const sellerData = await this.createUserIfNotExist(sellerDto, seller.chat_unique_user_identifier);

      const moderator = await this.dbServiceObj.findOne(this.userRepository, "id", data.moderator_id);
      if (!moderator) {
        return { "error_message": "user not found" };
      }
      const moderatorDto = {
        chatRoom: this.roomId,
        externalUserId:moderator.id?.toString(), 
        isModerator: true,
        email: moderator.email_id,
        username: `moderator_${moderator.first_name}_${moderator.last_name}`,
      };
      const moderatorData = await this.createUserIfNotExist(moderatorDto, moderator.chat_unique_user_identifier);

      const existingChannelData = await this.dbServiceObj.findOne(this.chatChannelsRepository, "channel_name", data.channel_name);
      if (!existingChannelData) {
        const payload = { enabled: true, notifyAllUsers: false, channelName: data.channel_name };
        response = (await axios.post(`${this.baseUrl}/consumer/api/v1/chatroom/${this.roomId}/channel`, payload, { params: this.params }))?.data;

        const memberIds = JSON.stringify([buyerData.uniqueUserIdentifier, sellerData.uniqueUserIdentifier, moderatorData.uniqueUserIdentifier]);

        const saveDto = { id: response._id, channel_name: response.channelName, enabled: response.enabled, notify_all_users: response.notifyAllUsers, room_id: response.roomId, members_unique_user_identifier: memberIds };
        const result = await this.dbServiceObj.saveData(saveDto, this.chatChannelsRepository);
        
        return {
          _id: response._id,
          channelName: data.channel_name,
          [data.buyer_id]: buyerData,
          [data.seller_id]: sellerData,
          [data.moderator_id]: moderatorData,
        }
      } else {
        return {  _id: existingChannelData.id,
          channelName: data.channel_name,
          [data.buyer_id]: buyerData,
          [data.seller_id]: sellerData,
          [data.moderator_id]: moderatorData, };
      }

    } catch (e) {
      console.log(e);
    }
  }

  async createUserIfNotExist(payload: any, uniqueUserIdentifier: string) {
    try {
      let existingUser = null;
      try {
        existingUser = (await axios.get(`${this.baseUrl}/consumer/api/v2/user/${uniqueUserIdentifier}`, { params: this.params }))?.data;
      } catch (e) {
        // user not found , create new user
        console.log(e?.response?.data?.message);
      }
      if (!existingUser) {
        const response = (await axios.post(this.baseUrl + '/consumer/api/v1/user', payload, { params: this.params })).data;
        const saveDto = {
          id: response.userId,
          access_token: response.accessToken,
          unique_user_identifier: response.uniqueUserIdentifier,
          user_name: response.username,
          email: payload.email,
          is_moderator: response.isModerator,
          room_id: payload.chatRoom,
          external_user_id: payload.externalUserId,
        };
        const saveResult = await this.dbServiceObj.saveData(saveDto, this.chatUsersRepository);
        const updateDto = await this.dbServiceObj.updateByColumnId(this.userRepository, {chat_unique_user_identifier: response.uniqueUserIdentifier}, "id", payload.externalUserId);
        return response;
      } else {
        if (existingUser.isModerator) {
          const user = await this.dbServiceObj.findOne(this.chatUsersRepository, "id", existingUser._id);
          if (user) {
            existingUser.accessToken = user.access_token;
          }
        }
        return existingUser;
      }
    } catch (e) {
      console.log(e);
    }
  }

  async getAllAcceptedBuyerOrders(userId: number) {
    const conditions: any = [
      { "column": "claimed_by", "operator": "NOT IN", "value": ["PENDING", "READY_TO_CLAME"] },
      { "column": "is_active", "operator": "=", "value": true },
    ];
    if (userId !== 883) {
      conditions.push({ "column": "buyer_id", "operator": "=", "value": userId });
    }

    return await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userPurchaseOrderRepository, null, conditions);
  }

  async getAllAcceptedSellerOrders(userId: number) {
    const conditions: any = [
      { "column": "claimed_by", "operator": "NOT IN", "value": ["PENDING", "READY_TO_CLAME"] },
      { "column": "is_active", "operator": "=", "value": true },
    ];
    if (userId !== 883) {
      conditions.push({ "column": "seller_id", "operator": "=", "value": userId },);
    }

    return await this.dbServiceObj.FindManyByMultipleConditionAndLeftJoin(this.userPurchaseOrderRepository, null, conditions);
  }

  async getAllUsersInChannel(channelName: string) {
    try {
      const channelData = await this.dbServiceObj.findOne(this.chatChannelsRepository, "channel_name", channelName);
      if (!channelData) {
        return { "error_message": "channel not found" }
      }

      const membersUniqueUserIdentifier = JSON.parse(channelData.members_unique_user_identifier);
      const blockedMembersUniqueUserIdentifier = JSON.parse(channelData.blocked_members_unique_user_identifier);
      const ids = membersUniqueUserIdentifier.map(id => id);
      const chatUsersData = await this.dbServiceObj.findManyByWhereIn(this.chatUsersRepository, "unique_user_identifier", ids);
      chatUsersData.forEach(data => {
        const uniqueUserIdentifier = data.unique_user_identifier;
        const isBlocked = !!blockedMembersUniqueUserIdentifier.find(id => id === uniqueUserIdentifier);
        data.is_user_blocked = isBlocked;
      });

      return chatUsersData;
    } catch (e) {
      console.log(e);
    }
  }

  async blockUserInChannel(channelName: string, uniqueUserIdentifier: string) {
    try {
      let blockedMemberIds = [];
      let blockedMemberIdsText = "";

      const channelData = await this.dbServiceObj.findOne(this.chatChannelsRepository, "channel_name", channelName);
      if (!channelData) {
        return { "error_message": "channel not found" }
      }

      blockedMemberIds = JSON.parse(channelData.blocked_members_unique_user_identifier);
      if (!blockedMemberIds.find(id => id === uniqueUserIdentifier)) {
        blockedMemberIdsText = JSON.stringify([...blockedMemberIds, uniqueUserIdentifier]);
        await this.dbServiceObj.updateByColumnId(this.chatChannelsRepository, { blocked_members_unique_user_identifier: blockedMemberIdsText }, "channel_name", channelName);
      } else {
        return { "error_message": "user already blocked" }
      }

      return "Sucess";
    } catch (e) {
      console.log(e);
    }
  }

  async unBlockUserInChannel(channelName: string, uniqueUserIdentifier: string) {
    try {
      let blockedMemberIds = [];
      let blockedMemberIdsText = "";

      const channelData = await this.dbServiceObj.findOne(this.chatChannelsRepository, "channel_name", channelName);
      if (!channelData) {
        return { "error_message": "channel not found" }
      }

      blockedMemberIds = JSON.parse(channelData.blocked_members_unique_user_identifier);
      blockedMemberIds = blockedMemberIds.filter(id => id !== uniqueUserIdentifier);
      blockedMemberIdsText = JSON.stringify(blockedMemberIds);
      await this.dbServiceObj.updateByColumnId(this.chatChannelsRepository, { blocked_members_unique_user_identifier: blockedMemberIdsText }, "channel_name", channelName);
      return "Sucess";
    } catch (e) {
      console.log(e);
    }
  }

  async makeUpdateChatRoomMember(roomId: any, payload: any) {
    try {
      const response = (await axios.post(`${this.baseUrl}/consumer/api/v2/room/${roomId}/member`, payload, { params: this.params }))?.data;
      return response;
    } catch (e) {
      console.log(e);
    }
  }

  async createModerator(payload: any) {
    try {
      const response = (await axios.post(`${this.baseUrl}/consumer/api/v1/user`, payload, { params: this.params }))?.data;
      return response;
    } catch (e) {
      console.log(e);
    }
  }

  async deleteChatRoom() {
    try {
      const response = (await axios.delete(`${this.baseUrl}/consumer/api/v1/chatroom/${this.roomId}`, { params: this.params }))?.data;
      return response;
    } catch (e) {
      console.log(e);
    }
  } */
  
  async saveForegroundBackgroundActivity(userId: string, foregroundBackgroundActivityDto: ForegroundBackgroundActivityDto) {
    let user = await this.dbServiceObj.findOne(this.userRepository, 'id', userId);
    if (!user) {
      return "Sorry user not found!";
    }
    let logoutResponse = await this.dbServiceObj.saveWithOutMapper(
      {
        email_id: user.email_id,
        event: foregroundBackgroundActivityDto.event,
        os_version: foregroundBackgroundActivityDto.os_version,
        ui_version: foregroundBackgroundActivityDto.ui_version,
        last_login_app_version: foregroundBackgroundActivityDto.last_login_app_version,
        device_id: foregroundBackgroundActivityDto.device_id,
      },
      user.id,
      this.userLogger,
    );
    if (logoutResponse) {
      return "Activity  successfully saved!";
    }
  }
}

