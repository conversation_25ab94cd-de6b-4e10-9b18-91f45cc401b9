import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { AppModule } from './app.module';
import { GlobalExceptionFilter } from '@bryzos/extended-widget-library';



async function bootstrap() {
	
	const app = await NestFactory.create(AppModule,{
      // logger: new BryzosLogger()
    }
	);

	const configService = app.get(ConfigService);

	if(process.env.NODE_ENV == "development") {

		const swagger_config = new DocumentBuilder()
			.setTitle('Bryzos widget Service')
			.setDescription('The widget Service APIs are used for onboarding and Purchase orders')
			.setVersion('1.0')
			.addBearerAuth()
			.addServer(process.env.SERVER_URL)
			.addTag('node-widget-service')
			.build();
		const document = SwaggerModule.createDocument(app, swagger_config);

		document.components.securitySchemes = {
			accesstoken: {
			  type: 'apiKey',
			  in: 'header',
			  name: 'accesstoken',
			  description: 'A Custom Header',
			},
		};
		
		SwaggerModule.setup('api', app, document);
	}

  	var bodyParser = require('body-parser');
	app.use(bodyParser.json({limit: process.env.REQUEST_JSON_SIZE+"mb"}));
	app.use(bodyParser.urlencoded({limit: process.env.REQUEST_JSON_SIZE+"mb", extended: true}));
  
	app.enableCors();
	// app.useGlobalFilters(new GlobalExceptionFilter());


	app.listen(configService.get<string>('PORT'),() => console.log(`Listening on port `,configService.get<string>('PORT')));
}
bootstrap();
