import { AddressDto, BaseDto } from "@bryzos/extended-widget-library";
import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsBoolean, IsEmail, IsInt, IsNotEmpty, IsNotEmptyObject, IsObject, IsOptional, IsString, ValidateNested, IsNumber } from "class-validator";

export class ArPaymentInfoDTO {
    @ApiProperty() @IsString() @IsNotEmpty() bank_name: string;
    @ApiProperty() @IsString() @IsNotEmpty() routing_number: string;
    @ApiProperty() @IsString() @IsNotEmpty() account_number: string;
    @ApiProperty() @IsString() @IsNotEmpty() reference_document_id: string;
    @ApiProperty() @IsInt() @IsNotEmpty() pgpm_mapping_id: number;
    payment_info_id: string;
}

export class PaymentInfoDTO {
    user_id: string;
    @IsNotEmpty() @IsInt() pgpm_mapping_id: number;
    @IsOptional() @IsNotEmpty() @IsString() pg_vendor_id: string;
    @IsOptional() @IsBoolean() is_payment_approved: boolean;
}

export class SellingPreferenceDto {
    @ApiProperty() @IsString() @IsNotEmpty() company_name: string;
    @ApiProperty() @IsOptional() @IsString() @IsNotEmpty() client_company: string;
    @ApiProperty() @IsNotEmptyObject() @IsObject() @ValidateNested({ each: true }) @Type(() => AddressDto) address?: AddressDto;
    @ApiProperty() @IsString() @IsNotEmpty() first_name: string;
    @ApiProperty() @IsString() @IsNotEmpty() last_name: string;
    @ApiProperty() @IsString() @IsNotEmpty() @IsEmail() email_id: string;
    @ApiProperty() @IsString() @IsNotEmpty() phone: string;
    @ApiProperty() @IsOptional() @IsString() stocking_location?: string;
    @ApiProperty() @IsString() @IsNotEmpty() send_invoices_to: string;
    @ApiProperty() @IsString() @IsNotEmpty() shipping_docs_to: string;
    @ApiProperty() @IsOptional() @IsString() @IsNotEmpty() w9_form_s3_url?: string;
    @ApiProperty() @IsOptional() @IsString() @IsNotEmpty() products_s3_url?: string;
    @ApiProperty() @IsOptional() @IsNotEmptyObject() @ValidateNested({ each: true }) @Type(() => ArPaymentInfoDTO)
    funding_settings?: ArPaymentInfoDTO;
}

export class SaveSellingPreferenceDto extends BaseDto {
    @Type(() => SellingPreferenceDto)
    @ApiProperty() data: SellingPreferenceDto;
}

export class CassSellerDto {
    @ApiProperty() @IsString() @IsNotEmpty() cass_unique_id: string;
    @ApiProperty() @IsString() @IsOptional() user_id: string;
    @ApiProperty() @IsString() @IsOptional() status: string;



}
export class saveCassSellerDto extends BaseDto {
    @Type(() => CassSellerDto)
    @ApiProperty() data: CassSellerDto;
}

export class SellerProfileViaMobileDto {
    @ApiProperty() @IsNotEmpty() @IsString() first_name: string;
    @ApiProperty() @IsNotEmpty() @IsString() last_name: string;
    @ApiProperty() @IsNotEmpty() @IsString() @IsEmail() email_id: string;
    @ApiProperty() @IsNotEmpty() @IsString() phone: string;
}
export class SaveSellerProfileViaMobileDto extends BaseDto {
    @Type(() => SellerProfileViaMobileDto)
    @ApiProperty() data: SellerProfileViaMobileDto;
}
export class SellerCompanyViaMobileDto {
    @ApiProperty() @IsNotEmpty() @IsString() company_name: string;
    @ApiProperty() @IsOptional() @IsString() @IsNotEmpty() client_company: string;
    @ApiProperty() @IsNotEmpty() @IsString() company_address_line1: string;
    @ApiProperty() @IsNotEmpty() @IsString() company_address_city: string;
    @ApiProperty() @IsNotEmpty() @IsNumber() company_address_state_id: number;
    @ApiProperty() @IsNotEmpty() @IsString() company_address_zip: string;
}
export class SaveSellerCompanyViaMobileDto extends BaseDto {
    @Type(() => SellerCompanyViaMobileDto)
    @ApiProperty() data: SellerCompanyViaMobileDto;
}
export class SellerStockInformationDto {
    @ApiProperty() @IsOptional() @IsString() stocking_location: string;
    @ApiProperty() @IsNotEmpty() @IsString() send_invoices_to: string;
    @ApiProperty() @IsNotEmpty() @IsString() shipping_docs_to: string;
}
export class SaveSellerStockInformationDto extends BaseDto {
    @Type(() => SellerStockInformationDto)
    @ApiProperty() data: SellerStockInformationDto;
}


export class SaveFundingSettingsDto extends BaseDto {
    @Type(() => ArPaymentInfoDTO)
    @ApiProperty() data: ArPaymentInfoDTO;
}