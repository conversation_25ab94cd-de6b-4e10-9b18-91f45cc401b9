export class Constants {
    public static PAYLOAD_TAG = "data";
    public static RESPONSE_TAG = "data";
    public static RESPONSE_CODE_OK = 200;
    public static BUYER = "BUYER";
    public static SELLER = "SELLER";
    public static LOW_PRICING = "LOW";
    public static HIGH_PRICING = "HIGH";
    public static GOOD_PRICING = "GOOD";
    public static USER_LOGIN = "LOG-IN";
    public static USER_LOGOUT = "LOG-OUT";
    public static FT = 'ft';
    public static LB = 'Lb';
    public static NEUTRAL = 'NEUTRAL';
    public static SALES_TAX_EXEMPTION_STATUS_PENDING = "Pending";
    public static PAYMENT_METHOD_BNPL = 'BUY_NOW_PAY_LATER';
    public static PAYMENT_METHOD_ACH_CREDIT = 'ACH_CREDIT';
    public static SEND_SUPER_ADMIN_EMAIL_FOR_SELLER_PAYMENT_SETUP = 'SEND_SUPER_ADMIN_EMAIL_FOR_SELLER_PAYMENT_SETUP';
    public static BALANCE_BUYER_ID = 'balance_buyer_id';
    public static VENDOR_BALANCE = 'BALANCE';
    public static BALANCE_SECRET_KEY = 'SECRET_KEY';
    public static DOMESTIC_MATERIAL_VALUE = 'Domestic (USA) Material Only';
    public static ORDER_STATUS_COMPLETED = 'Completed';
    public static Approved = 'Approved';
    public static FREIGHTTERMDELIVERED = 'Delivered';
    public static ORDER_STATUS_CANCELED = 'Canceled';
    public static ORDER_STATUS_OPEN = 'Open';
    public static ORDER_STATUS_CLOSE = 'Close';
    public static ORDERCANCELLED = "OrderCancelled";
    public static ORDERACTIVE = "Active";
    public static ORDERCOMPLETED = "Completed";
    public static NONE = "NONE";
    
    public static TRUVAULT_DOC_TYPE_SELLER = 'SELLER';
    public static TRUVAULT_DOC_TYPE_BUYER = 'BUYER';
    public static REFERENCE_DATA_VENDOR = 'REFERENCE_DATA';
    public static HOMEPAGE_REFERENCE_DATA_VENDOR = 'HOMEPAGE_REFERENCE_DATA';
    public static CACHE_KEY = 'CACHE_KEY';

    public static USER_ONBOARD_PENDING_REQUEST = 'USER_ONBOARD_PENDING_REQUEST';
    public static CASS_SUPPLIER_CREATED_SUCCESS = 'SUCCESS';
    public static VENDOR_CASS = 'CASS';
    public static PASSWORD = 'PASSWORD';
    public static CHECKOUT_VIA_BALANCE = 'CHECKOUT_VIA_BALANCE';
    public static ON = 'ON';
    public static ACTIVE = 'active';
    public static PREVIOUS = 'previous';

    public static USER_SEARCH_LINE_DETAILS_QTY = 'USER_SEARCH_LINE_DETAILS_QTY';

    public static REF_DATA_HOMEPAGE_VENDOR = 'REF_DATA_HOMEPAGE';
    public static PRODUCT_ENCRYPTION_KEY = 'PRODUCT_ENCRYPTION_KEY';
    public static NOTIFICATION_PRICE_PRODUCT_CHANGES = 'NOTIFICATION_PRICE_PRODUCT_CHANGES';

    public static ERROR_TAG='error_message';
    public static INTERNAL_NOTIFICATION='INTERNAL_NOTIFICATION';
    public static AWS_VENDOR = 'AWS';
    public static AWS_KEY = 'CREDENTIAL';
    public static USER_DELETED = 'USER_DELETED';

    public static lb="lb";
    public static HOMEPAGE_SHARE_PRODUCT_PRICING = 'HOMEPAGE_SHARE_PRODUCT_PRICING';
}