import { Injectable, UnauthorizedException } from '@nestjs/common';
import { AuthenticationDetails, CognitoUser, CognitoUserPool, CognitoUserSession } from 'amazon-cognito-identity-js';

@Injectable()
export class CognitoAuthService {
  private readonly userPoolId = process.env.AWS_USERPOOL_ID;
  private readonly clientId = process.env.AWS_CLIENT_ID;
  private readonly region = process.env.AWS_REGION;

  private readonly poolData = {
    UserPoolId: this.userPoolId,
    ClientId: this.clientId,
  };

  private userPool = new CognitoUserPool(this.poolData);

  async authenticateUser(email: string, password: string): Promise<{ access_token: string; token_id: string }> {
    const authenticationData = {
      Username: email,
      Password: password,
    };

    const authenticationDetails = new AuthenticationDetails(authenticationData);

    const userData = {
      Username: email,
      Pool: this.userPool,
    };

    const cognitoUser = new CognitoUser(userData);

    return new Promise<{ access_token: string; token_id: string }>((resolve, reject) => {
      cognitoUser.authenticateUser(authenticationDetails, {
        onSuccess: (session: CognitoUserSession) => {
          const access_token = session.getAccessToken().getJwtToken();
          const token_id = session.getIdToken().getJwtToken();
          resolve({access_token, token_id});
        },
        onFailure: (err) => {
          reject(new UnauthorizedException('Authentication failed'));
        },
      });
    });
  }
}
