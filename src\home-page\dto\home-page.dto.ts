import { ApiProperty, getSchemaPath } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty, IsEmail, IsString, IsEnum, IsOptional, ValidateIf, IsDecimal, IsArray, ValidateNested, IsNumber } from "class-validator";
import { BaseDto } from "src/base.dto";
import { Constants } from "src/Constants";

export class ProductPricing {
    @ApiProperty() @IsNotEmpty() product_id: number;
    @ApiProperty() @IsNotEmpty() @IsString() product_description: string;
    @ApiProperty() @IsDecimal() @ValidateIf((o) => ['ft'].includes(o.price_share_type)) @IsNotEmpty({message: 'Price per ft required',}) price_ft?: string;
    @ApiProperty() @IsDecimal() @ValidateIf((o) => ['lb'].includes(o.price_share_type)) @IsNotEmpty({message: 'Price per Lb required',}) price_lb?: string;
}

export class HomePageShareProductPricing {
    @ApiProperty() @IsNotEmpty() @IsEmail() from_email: string;
    @ApiProperty() @IsNotEmpty() @IsEmail() to_email: string;
    @ApiProperty() @IsNotEmpty() @IsString() @IsEnum([Constants.FT,Constants.lb], { message: 'Invalid price share type' }) price_share_type: string;
    @ApiProperty() @IsOptional() @IsNotEmpty() email_content: string;
    @ApiProperty({ type: () => [ProductPricing] }) @IsArray() 
    @ValidateNested({ each: true }) @Type(() => ProductPricing) products: ProductPricing[];
    from_user_type: string;
}

export class SaveHomePageShareProductPricing extends BaseDto {
    @Type(() => HomePageShareProductPricing)
    @ApiProperty() data: HomePageShareProductPricing;
}

export class HomePagePricingFeedbackDto {
    @ApiProperty() @IsNotEmpty() product_id: number;
    @ApiProperty() @IsNotEmpty() @IsString() product_description: string;
    @ApiProperty() @IsNotEmpty() @IsString() @IsEnum([Constants.HIGH_PRICING,Constants.LOW_PRICING,Constants.GOOD_PRICING], { message: 'Invalid feedback' }) feedback: string;
    @ApiProperty() @IsNotEmpty() @IsDecimal() price_ft: number;
    @ApiProperty() @IsNotEmpty() @IsDecimal() price_lb: number;
    @ApiProperty() @IsNotEmpty() @IsString() @IsEnum([Constants.FT,Constants.lb], { message: 'Invalid price share type' }) price_feedback_type: string;
}

export class SaveHomePagePricingFeedbackDto extends BaseDto {
    @Type(() => HomePagePricingFeedbackDto)
    @ApiProperty() data: HomePagePricingFeedbackDto;
}