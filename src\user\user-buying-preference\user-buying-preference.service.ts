import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataBaseService, ReferenceDataSettings} from '@bryzos/base-library';
import { Repository } from 'typeorm';
import { BuyingPreferenceDto, BuyerProfileDto, BuyerCompanyInfoDto, BuyerDeliveryDto, DeliveryAvailibilityDto, BnplRequestDto, ResaleCertificateDto } from '../dto/save-buying-preference.dto';
import { UserBuyingPreferenceMapper, UserDeliveryReceivingAvailibitityDetailsMapper, UserResaleCertificateMapper } from './user-buying-preference-mapper';
import { CompanyBuyNowPayLater, UserAchCredit, UserBuyingPreference, UserDeliveryReceivingAvailabilityDetails, UserResaleCertificate, PaymentInfo, User } from '@bryzos/extended-widget-library';
import { Constants } from 'src/Constants';
import { AwsQueue } from 'src/AwsQueue';
import { Balance } from 'src/Balance';
import { UserService } from '../user.service';

@Injectable()
export class UserBuyingPreferenceService {

  private maperClass = new UserBuyingPreferenceMapper();
  private dbServiceObj = new DataBaseService()

  constructor(
    private readonly awsQueue:AwsQueue,
    private readonly balance:Balance,
    private readonly userService: UserService,
    @InjectRepository(UserBuyingPreference) private readonly userBuyingPreferenceRepository: Repository<UserBuyingPreference>,
    @InjectRepository(UserResaleCertificate) private readonly userResaleCertificateRepository: Repository<UserResaleCertificate>,
    @InjectRepository(UserDeliveryReceivingAvailabilityDetails) private readonly userDeliveryReceivingAvailabilityDetailsRepository: Repository<UserDeliveryReceivingAvailabilityDetails>,
    @InjectRepository(UserAchCredit) private readonly userAchCreditRepository: Repository<UserAchCredit>,
    @InjectRepository(CompanyBuyNowPayLater) private readonly companyBuyNowPayLaterRepository: Repository<CompanyBuyNowPayLater>,
    @InjectRepository(ReferenceDataSettings) private readonly referenceDataSettingsRepository: Repository<ReferenceDataSettings>,
    @InjectRepository(PaymentInfo) private readonly paymentInfoRepository: Repository<PaymentInfo>,
    @InjectRepository(User) private readonly userRepository: Repository<User>,
    ) { }


  async create(createDto: BuyingPreferenceDto, userId: string) {
    let buyingPreference = createDto;

    if([Constants.PAYMENT_METHOD_BNPL].includes(buyingPreference.default_payment_method)) {
      //check if BNPL is set
      let bnplSettings = await this.dbServiceObj.findByUserId(this.companyBuyNowPayLaterRepository,userId);
      if(bnplSettings === undefined) return { "error_message": "Please setup BNPL payment." };
    }

    if(buyingPreference.client_company === null) {
      return { "error_message": "Please fill your company." }
    }

    let getUserData = await this.dbServiceObj.findOne(this.userRepository,"id",userId)

    buyingPreference["company_id"] = getUserData.company_id;
    //save buying preference
    await this.dbServiceObj.save(buyingPreference, this.maperClass, userId, this.userBuyingPreferenceRepository);

    if(buyingPreference.client_company) {
      this.userService.updateUser({ 'client_company': buyingPreference.client_company, first_name: buyingPreference.first_name, last_name: buyingPreference.last_name }, userId);
    }
    
    if(buyingPreference.hasOwnProperty('resale_certificate') && Array.isArray(buyingPreference.resale_certificate)) {
      let certificate = await this.saveResaleCertificate(userId, buyingPreference.resale_certificate);
    }

    if (buyingPreference.hasOwnProperty('user_delivery_receiving_availability_details') && Array.isArray(buyingPreference.user_delivery_receiving_availability_details)) {
      //deactivate all entries for user
      await this.dbServiceObj.markInActiveMultipleWhere(this.userDeliveryReceivingAvailabilityDetailsRepository,{'user_id': userId});

      await this.dbServiceObj.saveMany(buyingPreference.user_delivery_receiving_availability_details, new UserDeliveryReceivingAvailibitityDetailsMapper(), userId, this.userDeliveryReceivingAvailabilityDetailsRepository, true);
    }

    let response = await this.getBuyingPreferenceData(userId);
    return response;
  }

  async getBuyingPreferenceData(userId: string) {
    let balanceBuyerId = null;
    let buyingPreferenceData = await this.dbServiceObj.findByUserId(this.userBuyingPreferenceRepository, userId);
    
    let resaleCertificate = await this.dbServiceObj.findAllByUserId(this.userResaleCertificateRepository, userId);

    let userDeliveryReceivingAvailabilityDetails = await this.dbServiceObj.findAllAndOrderByWeekDays(this.userDeliveryReceivingAvailabilityDetailsRepository, userId);

    let achCredit = await this.dbServiceObj.findByUserId(this.userAchCreditRepository, userId);

    const checkoutViaBalance = await this.dbServiceObj.findOne(this.referenceDataSettingsRepository,"name",Constants.CHECKOUT_VIA_BALANCE);
    let checkInBalance = checkoutViaBalance.value;

    let bnplSettings = await this.dbServiceObj.findOneWithOrComparision(this.companyBuyNowPayLaterRepository, {'user_id': userId} , 'is_approved', [null, '1']);
    if(bnplSettings != undefined) {
      bnplSettings.balance_credit_limit = 0;
      bnplSettings.balance_available_credit_limit = 0;
      balanceBuyerId = checkInBalance === Constants.ON ? await this.balance.getBuyerId(userId) : null;
    }
    
    if(buyingPreferenceData) {
      buyingPreferenceData.resale_certificate = resaleCertificate;
      buyingPreferenceData.user_delivery_receiving_availability_details = userDeliveryReceivingAvailabilityDetails;
      buyingPreferenceData.ach_credit = achCredit,
      buyingPreferenceData.bnpl_settings = bnplSettings
      
      if(buyingPreferenceData.bnpl_settings){
        buyingPreferenceData.bnpl_settings.outstanding_amount = 0; 
      }

      if(checkInBalance === Constants.ON) {
        if(balanceBuyerId !== null && bnplSettings != undefined) {
          let balanceKey = await this.balance.getBalanceKey();
          let balanceResponse =  await this.balance.getBuyerCreditLimit(balanceBuyerId,balanceKey);
          buyingPreferenceData.bnpl_settings.balance_credit_limit = balanceResponse['balance_credit_limit'];
          buyingPreferenceData.bnpl_settings.balance_available_credit_limit = balanceResponse['balance_available_credit_limit'];
          buyingPreferenceData.bnpl_settings.outstanding_amount = balanceResponse['balance_credit_limit'] - balanceResponse['balance_available_credit_limit'];
        }
      }else{
        if(bnplSettings != undefined) {

          bnplSettings.bryzos_credit_limit = bnplSettings.bryzos_credit_limit ? bnplSettings.bryzos_credit_limit : 0;
          bnplSettings.bryzos_available_credit_limit = bnplSettings.bryzos_available_credit_limit ? bnplSettings.bryzos_available_credit_limit : 0;

          buyingPreferenceData.bnpl_settings.balance_credit_limit = bnplSettings.bryzos_credit_limit;
          buyingPreferenceData.bnpl_settings.balance_available_credit_limit = bnplSettings.bryzos_available_credit_limit;
          buyingPreferenceData.bnpl_settings.outstanding_amount = bnplSettings.bryzos_credit_limit - bnplSettings.bryzos_available_credit_limit;
        }
      }
    } else {
      buyingPreferenceData = {'ach_credit': achCredit, 'user_delivery_receiving_availability_details': userDeliveryReceivingAvailabilityDetails, 'bnpl_settings': bnplSettings};
      
    }
    
    return buyingPreferenceData;
  }

  async keyValueArrayMatches(object1: any, object2: any, keyValueArray: any) {
    return keyValueArray.every(([key, value]) => object1[key] === object2[key] && object1[key] === value);
  }

  async saveResaleCertificate(userId, resaleCertificate) {
    let resaleCertificateId = null;
    let allCurrentResaleCert = await this.dbServiceObj.findAllByUserId(this.userResaleCertificateRepository, userId);
    
    let notDeletable = allCurrentResaleCert.filter(obj => obj.is_deletable == false);
    const stateIds = notDeletable.map(obj => obj.state_id);
    const filterResaleCertificate = resaleCertificate.filter(obj => !stateIds.includes(obj.state_id));

    let getUserData = await this.dbServiceObj.findOne(this.userRepository,"id",userId);
    const companyId = getUserData.company_id;

    let existingIds = resaleCertificate.filter(el => el.hasOwnProperty('id')).map(el => el.id).map(String);
    //update existing certificates with given values
    await this.dbServiceObj.markInactiveWithNotInCondition(this.userResaleCertificateRepository, userId, 'id', existingIds);
    for(let certificate of filterResaleCertificate) {

      //deactivate if same state_id certificate exists 
      await this.dbServiceObj.markInActiveMultipleWhere(this.userResaleCertificateRepository, {'user_id': userId, 'state_id': certificate.state_id});

      let existingCert = await allCurrentResaleCert.find(cert => cert.id === certificate.id);
      
      let keyValueArray = [
        ['state_id', certificate.state_id], 
        ['cerificate_url_s3', certificate.cerificate_url_s3], 
        ['expiration_date', certificate.expiration_date]
      ];

      //check if user has changed any above parameter for existings certificate
      let match = false;
      if(existingCert != undefined) {
        match = await this.keyValueArrayMatches(existingCert, certificate, keyValueArray);
        if(!match) {
          certificate.status = Constants.SALES_TAX_EXEMPTION_STATUS_PENDING;
          await this.dbServiceObj.markInActive('id', existingCert.id, this.userResaleCertificateRepository);
        } else {
          //reactivate in user_resale_certificate but don't send email
          resaleCertificateId = await this.dbServiceObj.updateByColumnId(this.userResaleCertificateRepository, {'is_active': true}, 'id', existingCert.id);
        }
      }

      if(certificate.status == undefined) certificate.status = Constants.SALES_TAX_EXEMPTION_STATUS_PENDING;
      else certificate.status = certificate.status;
      
      if(!match) {
        certificate["company_id"] = companyId;
        resaleCertificateId = await this.dbServiceObj.save(certificate, new UserResaleCertificateMapper(), userId, this.userResaleCertificateRepository,true);
        await this.awsQueue.sendSalesTaxApproval(resaleCertificateId);
      }
    }
    return resaleCertificateId;
  }

  async saveBuyerProfile(buyerProfileDto: BuyerProfileDto, userId: string) {
    let response = null;
    let buyerProfile = buyerProfileDto;

    const userUpdateDto = { first_name: buyerProfile.first_name, last_name: buyerProfile.last_name };
    await this.userService.updateUser(userUpdateDto, userId);

    let updateData = await this.dbServiceObj.updateWithoutMapper(buyerProfile, 'user_id', userId, this.userBuyingPreferenceRepository);
    
    if(updateData){
      response = "Saved Successfully"
    }else{
      response= { "error_message": "Something went wrong!" };
    }

    return response;
  }

  async saveBuyerCompanyInfo(buyerCompanyDto: BuyerCompanyInfoDto, userId: string) {
    let response=null;
    let buyerCompanyInfo = buyerCompanyDto;
    if(buyerCompanyInfo.client_company === null ) {
      return { "error_message": "Please fill your company." }
    }

    let getUserData = await this.dbServiceObj.findOne(this.userRepository,"id",userId)

    buyerCompanyInfo["company_id"] = getUserData.company_id;
    let updateData = await this.dbServiceObj.updateWithoutMapper(buyerCompanyInfo, 'user_id', userId, this.userBuyingPreferenceRepository);
    if(buyerCompanyInfo.client_company) {
      this.userService.updateUser({'client_company': buyerCompanyInfo.client_company}, userId);
    }
    if(updateData){
      response = "Saved Successfully"
    }else{
      response= { "error_message": "Something went wrong!" };
    }
    return response;
  }

  async saveBuyerDeliverAddInfo(buyerDeliverDto: BuyerDeliveryDto, userId: string) {
    let response = null;

    let buyerDeliveryInfo = buyerDeliverDto;
 
    let updateData = await this.dbServiceObj.updateWithoutMapper(buyerDeliveryInfo, 'user_id', userId, this.userBuyingPreferenceRepository);

    if(updateData){
      response = "Saved Successfully"
    }else{
      response= { "error_message": "Something went wrong!" };
    }

    return response; 
  }
  
  async saveBnplRequest(userId: string, payloadData: BnplRequestDto) {
    let response = null;
    //soft delete existing payment id
    await this.dbServiceObj.markInActiveMultipleWhere(this.paymentInfoRepository, {'user_id': userId, 'pgpm_mapping_id': payloadData.pgpm_mapping_id});
    let paymentInfoId = await this.dbServiceObj.saveWithOutMapper({'user_id': userId, 'pgpm_mapping_id': payloadData.pgpm_mapping_id}, userId, this.paymentInfoRepository);

    //soft delete existing BNPL request
    await this.dbServiceObj.markInActiveMultipleWhere(this.companyBuyNowPayLaterRepository, {'user_id': userId});
    let responseId = await this.dbServiceObj.saveWithOutMapper({'ein_number': payloadData.ein_number, 'duns': payloadData.duns_number, 'requested_credit_limit': payloadData.desired_credit_limit, 'payment_info_id': paymentInfoId, 'reference_document_id': payloadData.reference_document_id}, userId, this.companyBuyNowPayLaterRepository);

    //send admin BNPL approval request email
    this.awsQueue.sendDataToAwsQueue(responseId, 'WIDGET_BRYZOS_BUCK_APPROVAL_REQUEST', 'New Register for Bryzos Approval!', process.env.MESSAGE_NODE_EMAIL_QUEUE);
    response = 'Request saved successfully';
    return response;
  }

  async saveDefaultPaymentMethod(userId: string, payload:any) {
    let response = null;
    if(payload.hasOwnProperty('default_payment_method') && payload.default_payment_method != '') {
      await this.dbServiceObj.saveOrUpdateWithOutMapper({"default_payment_method": payload.default_payment_method}, userId, this.userBuyingPreferenceRepository);
      response = 'Saved successfully';
    }
    else {
      response= { "error_message": "Payment method not found" };
    }
    return response;
  }

  async saveReceivingHrsInfo(buyerReceivingHrsDto: DeliveryAvailibilityDto, userId: string) {
    let response = null;
    let updateData = null;
    let buyerReceivingHrs = buyerReceivingHrsDto;
  
    //deactivate all entries for user
    await this.dbServiceObj.markInActiveMultipleWhere(this.userDeliveryReceivingAvailabilityDetailsRepository,{'user_id': userId});

    updateData = await this.dbServiceObj.saveMany(buyerReceivingHrs, new UserDeliveryReceivingAvailibitityDetailsMapper(), userId, this.userDeliveryReceivingAvailabilityDetailsRepository, true);
    
    if(updateData)
      response = "Saved Successfully"
    else
      response= { "error_message": "Something went wrong!" };
    
    return response;
  }

  async saveDocumentLibrary(buyerDocumentLibDto: ResaleCertificateDto, userId: string) {
    let response = null;
    let certificate = null;
    let buyerDucumentLib = buyerDocumentLibDto;

    certificate = await this.saveResaleCertificate(userId, buyerDucumentLib);
    
    if(certificate)
      response = "Saved Successfully"
    else
      response= { "error_message": "Something went wrong!" };
    
    return response;
  }
}
