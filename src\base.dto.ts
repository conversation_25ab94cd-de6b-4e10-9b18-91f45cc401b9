import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import {  IsObject, ValidateNested ,IsArray, IsEmail, IsNotEmpty, IsNumber, IsOptional, IsString, ValidateIf } from "class-validator";

export class BaseDto {
    
    @IsObject()
    @ValidateNested({ each: true })
    data?: any;
}

export class AddressDto {
    @ApiProperty() @IsString() @IsNotEmpty() location_nick_name: string;
    @ApiProperty() @IsString() @IsNotEmpty() line1: string;
    @ApiProperty({ nullable: true}) @IsOptional() @IsString() @IsNotEmpty() line2: string;
    @ApiProperty() @IsString() @IsNotEmpty() city: string;
    @ApiProperty() @IsString() @IsNotEmpty() state_id: string;
    @ApiProperty() @IsString() @IsNotEmpty() zip: string;

}