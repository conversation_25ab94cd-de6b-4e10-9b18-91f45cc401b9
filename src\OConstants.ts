
import { ReferenceDataGeneralSettings, UserOrderRatings, UserProductTagMapping, UserPurchaseOrder, UserViewedPurchaseOrder, ReferenceDataCassDisbursementMethod, UserMainCompany, UserPendingCompanyRequests, ReferenceDataProductsHomepage, AdminLogNotification, UserDeleteAccount, HomepageSafeUploads, HomepageSafeConfig, UserWebhookSubscription, ChatUsers } from "@bryzos/extended-widget-library";
import { CompanyBuyNowPayLater, PaymentInfo, ReferenceDataBryzosTermsConditions, ReferenceDataDeliveryDate, ReferenceDataPGPMMapping, ReferenceDataResaleCertExpiration, ReferenceDataStates, ReferenceDatum, User, UserAchCredit, UserArPaymentInfo, UserBuyingPreference, UserDeliveryReceivingAvailabilityDetails, UserLogger, UserPricingFeedback, UserResaleCertificate, UserSellingPreference, UserShareProductPricing, UserShareWidgetRequest, WidgetTermsCondtionUserActions,ReferenceDataProductsWidget,ReferenceDataProductsPreview ,ReferenceDataProductsPrevious, ReferenceDataSalesTax, ReferenceDataOrderStatus, UserCreatepoOpenClose, UserSearchAnalytics, ReferenceEmailEvent, UserOnboradPendingRequests, LogCassSupplier, AdminLogReferenceDataProductRevert, ReferenceDataProductsLogVersion, UserSearchLineDetails, UserPurchaseOrderLine, ReferenceDataStateZipcode,UserSearchPriceDetails,UserSearchPriceScreenMoveOut,ReferenceDataDesktopNotification, HomepagePricingFeedback,HomepageSearchAnalytics,HomepageShareWidgetRequest,HomepageShareProductPricing} from "@bryzos/extended-widget-library";
import { ReferenceDataSettings, AwsUtilityV3, ReferenceDataProducts } from "@bryzos/base-library";
import { AwsQueue } from "./AwsQueue";
import { Balance } from "./Balance";
import { ReferenceDataService } from "./reference-data/reference-data.service";
import { UserBuyingPreferenceService } from "./user/user-buying-preference/user-buying-preference.service";
import { UserSellingPreferenceService } from "./user/user-selling-preference/user-selling-preference.service";
import { UserService } from "./user/user.service";
import { OrderService } from "./order/order.service";
import { ExternalAffairsService } from "./external-affairs/external-affairs.service";
import { PdfMakerService } from "@bryzos/pdf-maker";
import { ExternalApiService } from "./external-api/external-api.service";
import { HomePageService } from "./home-page/home-page.service";
import { AwsTextractService } from "./AwsTextractService";

export class OConstants {
    public static EntityArray = [ReferenceDataProducts, ReferenceDataGeneralSettings, ReferenceDataBryzosTermsConditions, ReferenceDataStates, ReferenceDataPGPMMapping,User, ReferenceDatum, UserPricingFeedback, UserShareWidgetRequest, UserLogger, UserShareProductPricing,WidgetTermsCondtionUserActions,UserBuyingPreference,UserResaleCertificate,UserDeliveryReceivingAvailabilityDetails,UserAchCredit,CompanyBuyNowPayLater,UserArPaymentInfo,PaymentInfo,UserSellingPreference,ReferenceDataDeliveryDate,ReferenceDataResaleCertExpiration,UserOrderRatings,UserPurchaseOrder, ReferenceDataProductsWidget,UserProductTagMapping,ReferenceDataProductsPreview, ReferenceDataProductsPrevious, UserViewedPurchaseOrder,ReferenceDataSalesTax,ReferenceDataOrderStatus,UserCreatepoOpenClose,UserSearchAnalytics, ReferenceEmailEvent, UserOnboradPendingRequests, LogCassSupplier,ReferenceDataCassDisbursementMethod, ReferenceDataSettings,AdminLogReferenceDataProductRevert,ReferenceDataProductsLogVersion, UserSearchLineDetails,UserPurchaseOrderLine, ReferenceDataStateZipcode, UserMainCompany, UserPendingCompanyRequests,UserSearchPriceDetails,UserSearchPriceScreenMoveOut,ReferenceDataProductsHomepage, AdminLogNotification,ReferenceDataDesktopNotification,UserDeleteAccount,HomepageSafeUploads, HomepageSafeConfig, UserWebhookSubscription, HomepagePricingFeedback,HomepageSearchAnalytics,HomepageShareWidgetRequest,HomepageShareProductPricing, ChatUsers];

    public static ServiceArray = [OrderService, UserService, ReferenceDataService, UserBuyingPreferenceService, UserSellingPreferenceService,AwsQueue,Balance,ExternalAffairsService, PdfMakerService, AwsUtilityV3, ExternalApiService, HomePageService, AwsTextractService];

}
