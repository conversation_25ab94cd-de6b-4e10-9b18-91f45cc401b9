import { BaseLibraryService, DataBaseService } from '@bryzos/base-library';
import { PaymentInfo, UserArPaymentInfo, UserSellingPreference, LogCassSupplier, UserMainCompany ,User } from '@bryzos/extended-widget-library';
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AwsQueue } from 'src/AwsQueue';
import { Constants } from 'src/Constants';
import { Repository } from 'typeorm';
import { SellingPreferenceDto, CassSellerDto, SellerProfileViaMobileDto, SellerCompanyViaMobileDto, SellerStockInformationDto, ArPaymentInfoDTO } from '../dto/save-selling-preference.dto';
import { ArPaymentInfoMapper, PaymentInfoMapper, SellingPreferenceMapper } from './user-selling-preference-mapper';
import { UserService } from '../user.service';

@Injectable()
export class UserSellingPreferenceService {
    private dbServiceObj = new DataBaseService()
    
    constructor(
        private readonly awsQueue:AwsQueue,
        private readonly baseLibraryService : BaseLibraryService,
        private readonly userService: UserService,
        @InjectRepository(UserArPaymentInfo) private readonly userArPaymentInfoRepository: Repository<UserArPaymentInfo>,
        @InjectRepository(PaymentInfo) private readonly paymentInfoRepository: Repository<PaymentInfo>,
        @InjectRepository(UserSellingPreference) private readonly userSellingPreferenceRepository: Repository<UserSellingPreference>,
        @InjectRepository(LogCassSupplier) private readonly logCassSupplierRepository: Repository<LogCassSupplier>,
        @InjectRepository(UserMainCompany) private readonly userMainCompanyRepository: Repository<UserMainCompany>,
        @InjectRepository(User) private readonly userRepository: Repository<User>,

    ) {}

    async create(createSelleingPreferenceDto: SellingPreferenceDto, userId: string) 
    {
        if(createSelleingPreferenceDto.client_company === null ) {
            return { "error_message": "Please fill your company." }
        }
        let getUserData = await this.dbServiceObj.findOne(this.userRepository,"id",userId)

        createSelleingPreferenceDto["company_id"] = getUserData.company_id;

        await this.dbServiceObj.save(createSelleingPreferenceDto, new SellingPreferenceMapper(), userId, this.userSellingPreferenceRepository);
        if(createSelleingPreferenceDto.client_company) {
            this.userService.updateUser({ 'client_company': createSelleingPreferenceDto.client_company, first_name: createSelleingPreferenceDto.first_name, last_name: createSelleingPreferenceDto.last_name }, userId);
        }
        
        if(createSelleingPreferenceDto.hasOwnProperty('funding_settings')) {
            let arPaymentInfo = createSelleingPreferenceDto.funding_settings;
            await this.saveFundingSettings(userId, arPaymentInfo);
        }
        let response = await this.getSellingPreferenceData(userId);
        return response;
    }

    async getSellingPreferenceData(userId: string) 
    {
        let sellingPreferenceData = null;
        let arPaymentInfo = null;
        sellingPreferenceData = await this.dbServiceObj.findByUserId(this.userSellingPreferenceRepository, userId);
        if(sellingPreferenceData !== undefined) {
            arPaymentInfo = await this.dbServiceObj.findByUserId(this.userArPaymentInfoRepository, userId);
            sellingPreferenceData.funding_settings = arPaymentInfo;
        } else {
            sellingPreferenceData = null;
        }
        return sellingPreferenceData;
    }

    async saveCassSeller(cassSellerDto: CassSellerDto, userId: string)
    {
        let response = null;
        cassSellerDto.user_id=userId;
        cassSellerDto.status=Constants.CASS_SUPPLIER_CREATED_SUCCESS;

        let checkCassSellerAlreadyExists = await this.dbServiceObj.findAllByUserId(this.logCassSupplierRepository, userId);

        if(checkCassSellerAlreadyExists && checkCassSellerAlreadyExists.length>0){
            await this.dbServiceObj.markInActiveMultipleWhere(this.logCassSupplierRepository,{'user_id': userId});
        }

        if(userId && cassSellerDto.cass_unique_id){
            await this.dbServiceObj.saveData(cassSellerDto,this.logCassSupplierRepository);
        }
        return response = 'Successfully Uploaded';
    }

    async saveSellerProfile(payload:SellerProfileViaMobileDto,userId){
        payload["user_id"] = userId

        const userUpdateDto = { first_name: payload.first_name, last_name: payload.last_name };
        await this.userService.updateUser(userUpdateDto, userId);

        const saveOrUpdate = await this.dbServiceObj.saveOrUpdateByMultipleWhere(payload,this.userSellingPreferenceRepository,{"user_id":userId});
        if(saveOrUpdate){
            return "Saved successfully";
        }else{
            return {"error_message":"Something went wrong!"};
        }
    }

    async saveSellerCompany(payload:SellerCompanyViaMobileDto,userId:string){
        if(payload.client_company === null ) {
            return { "error_message": "Please fill your company." }
        }
        let getUserData = await this.dbServiceObj.findOne(this.userRepository,"id",userId)

        payload["company_id"] = getUserData.company_id;
        payload["user_id"] = userId;
        
        const saveOrUpdate = await this.dbServiceObj.saveOrUpdateByMultipleWhere(payload,this.userSellingPreferenceRepository,
        {"user_id":userId});
        
        if(payload.client_company) {
            this.userService.updateUser({'client_company': payload.client_company}, userId);
        }
        
        if(saveOrUpdate){
            return "Saved successfully";
        }else{
            return {"error_message":"Something went wrong!"};
        }
    }

    async saveSellerStockInformation(payload:SellerStockInformationDto,userId:string){
        payload["user_id"]=userId
        const saveOrUpdate = await this.dbServiceObj.saveOrUpdateByMultipleWhere(payload,this.userSellingPreferenceRepository,{"user_id":userId});
        if(saveOrUpdate){
            return "Saved successfully";
        }else{
            return {"error_message":"Something went wrong!"};
        }
    }

    async saveFundingSettings(userId: string, arPaymentInfo:ArPaymentInfoDTO) {
        let response = null;
        let paymentInfoId = await this.dbServiceObj.save({ 'user_id': userId, 'pgpm_mapping_id': arPaymentInfo.pgpm_mapping_id}, new PaymentInfoMapper(), userId, this.paymentInfoRepository);
        if(paymentInfoId != undefined) {
            arPaymentInfo.payment_info_id = paymentInfoId;
            let sellerPaymentId = await this.dbServiceObj.save(arPaymentInfo, new ArPaymentInfoMapper(), userId, this.userArPaymentInfoRepository);
            if(sellerPaymentId !== undefined) {
                //check settings
                let settings = await this.baseLibraryService.getReferenceSetting(Constants.SEND_SUPER_ADMIN_EMAIL_FOR_SELLER_PAYMENT_SETUP);
                if(settings == 'true')
                    this.awsQueue.sendSellerPaymentSetup(sellerPaymentId, "true");
                else
                    this.awsQueue.sendSellerPaymentSetup(sellerPaymentId);
                
                response = 'Funding settings saved successfully'
            }
        }
        return response;
    }

    async saveSellerDocument(userId: string, payload: any) {
        let response = null;
        if(payload.hasOwnProperty('w9_form_s3_url') && payload.w9_form_s3_url != '') {
            await this.dbServiceObj.saveOrUpdateWithOutMapper({"w9_form_s3_url": payload.w9_form_s3_url}, userId, this.userSellingPreferenceRepository);
            response = 'Saved successfully';
        } else if(payload.hasOwnProperty('products_s3_url') && payload.products_s3_url != '') {
            await this.dbServiceObj.saveOrUpdateWithOutMapper({"products_s3_url": payload.products_s3_url}, userId, this.userSellingPreferenceRepository);
            response = 'Saved successfully';
        }
        else {
            response= { "error_message": "No data found" };
        }
        return response;
    }
}


