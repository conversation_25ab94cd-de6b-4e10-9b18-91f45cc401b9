import { SwaggerErrorResponse } from "@bryzos/extended-widget-library";
import { ApiProperty, getSchemaPath } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty, IsEmail, IsString, IsEnum, IsOptional, ValidateIf, IsDecimal, IsArray, ValidateNested, IsNumber, IsInt } from "class-validator";
import { BaseDto } from "src/base.dto";
import { Constants } from "src/Constants";



export class PORatingsDto {
    @ApiProperty() @IsNotEmpty() @IsString() po_number: string;
    @ApiProperty() @IsNotEmpty() @IsDecimal() rating: string;
    user_type: string;
    
}

export class SavePORatingsDto extends BaseDto {
    @Type(() => PORatingsDto)
    @ApiProperty() data: PORatingsDto;
}