import { MiddlewareConsumer, Module } from '@nestjs/common';
import { HomePageController } from './home-page.controller';
import { LoggerMiddleware } from '@bryzos/extended-widget-library';
import { SharedModule } from 'src/shared.module';

// @Module({
//   imports : [DataBaseService,BaseLibraryModule,TypeOrmModule.forFeature(OConstants.EntityArray)],
//   controllers: [HomePageController],
//   providers: OConstants.ServiceArray
// })

@Module({
  imports: [SharedModule],
  controllers: [HomePageController],
  providers: [SharedModule],
  exports: [SharedModule],
})

export class HomePageModule {
  configure(consumer: MiddlewareConsumer) {
		consumer
		  .apply(LoggerMiddleware)
		  .forRoutes('/homepage');
		}
}
