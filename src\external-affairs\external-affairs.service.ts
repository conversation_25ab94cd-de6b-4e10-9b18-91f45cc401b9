import { Injectable } from '@nestjs/common';
import { CreateExternalAffairDto } from './dto/create-external-affair.dto';
import { UpdateExternalAffairDto } from './dto/update-external-affair.dto';
import { DataBaseService } from '@bryzos/base-library';
import { InjectRepository } from '@nestjs/typeorm';
import { CompanyBuyNowPayLater, UserArPaymentInfo } from '@bryzos/extended-widget-library';
import { Repository } from 'typeorm';
import { Constants } from 'src/Constants';

@Injectable()
export class ExternalAffairsService {
  private dbServiceObj = new DataBaseService()

      
  constructor(
    @InjectRepository(UserArPaymentInfo) private readonly userArPaymentInfoRepository: Repository<UserArPaymentInfo>,
    @InjectRepository(CompanyBuyNowPayLater) private readonly cbnplRepository: Repository<CompanyBuyNowPayLater>,

  ){}
  create(createExternalAffairDto: CreateExternalAffairDto) {
    return 'This action adds a new externalAffair';
  }

  findAll() {
    return `This action returns all externalAffairs`;
  }

  findOne(id: number) {
    return `This action returns a #${id} externalAffair`;
  }

  update(id: number, updateExternalAffairDto: UpdateExternalAffairDto) {
    return `This action updates a #${id} externalAffair`;
  }

  remove(id: number) {
    return `This action removes a #${id} externalAffair`;
  }
  async getAllDocuments(type:string){

    let documentData,vaultUrl,paymentInfo;

    if(type == Constants.TRUVAULT_DOC_TYPE_BUYER){
      vaultUrl = process.env.TRUVALUT_BUYER_URL;
    }else{
      vaultUrl = process.env.TRUVALUT_SELLER_URL;
    }
    documentData = await this.getTruvalutDocs(vaultUrl);

    if (!documentData || typeof documentData !== 'object' || !documentData.hasOwnProperty('data') || typeof documentData.data !== 'object' || !documentData.data.hasOwnProperty('items')) {
      return { "error_message": "Something went wrong!" };
    }

    const promises = await documentData.data.items.map(async (data) => {

      if(type == Constants.TRUVAULT_DOC_TYPE_BUYER){
        paymentInfo = await this.dbServiceObj.findOne(this.cbnplRepository,'reference_document_id',data.id);
      }else{
        paymentInfo = await this.dbServiceObj.findOne(this.userArPaymentInfoRepository,'reference_document_id',data.id);
      }

      if(paymentInfo){
        data.document = Buffer.from(data.document, "base64").toString();
        data.userId =  paymentInfo.user_id;
        return data;
      }
    });

    let allDocuments = await Promise.all(promises);
    allDocuments = allDocuments.filter(n => n);
    return allDocuments;
  }

  getTruvalutDocs = (vaultUrl)  => new Promise(async function (resolve, reject) {
    const axios = require('axios');

    let config = {
      method: 'get',
      maxBodyLength: Infinity,
      url: vaultUrl,
      headers: { 
        'Authorization': 'Basic '+process.env.TRUVALUT_API_KEY
      }
    };
    
    axios.request(config)
    .then((response) => {
      resolve(response.data);
    })
    .catch((error) => {
      console.log(error);
      resolve(error);
    });
    
});

}
