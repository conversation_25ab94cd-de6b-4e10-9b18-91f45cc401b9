import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { getConnectionOptions } from 'typeorm';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { UserModule } from './user/user.module';
import { ReferenceDataModule } from './reference-data/reference-data.module';
import { AwsQueue } from './AwsQueue';
import { OrderModule } from './order/order.module';
import { ExternalAffairsModule } from './external-affairs/external-affairs.module';
import { ExternalApiModule } from './external-api/external-api.module';
import { HomePageModule } from './home-page/home-page.module';
import { ExceptionService } from '@bryzos/extended-widget-library';
import { configuration } from 'config/configuration';
@Module({
	imports: [ConfigModule.forRoot({  envFilePath: `${process.cwd()}/config/${process.env.NODE_ENV}.env`, isGlobal: true, load: [configuration] }),TypeOrmModule.forRootAsync({
		useFactory: async () =>
		Object.assign(await getConnectionOptions(), {
			autoLoadEntities: true,
		}),
	}), UserModule, ReferenceDataModule, OrderModule, ExternalAffairsModule, ExternalApiModule, HomePageModule],
	controllers: [AppController],
	providers: [AppService, ExceptionService],
})
export class AppModule {}
