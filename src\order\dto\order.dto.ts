import { SwaggerErrorResponse } from "@bryzos/extended-widget-library";
import { ApiProperty, getSchemaPath } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty, IsEmail, IsString, IsEnum, IsOptional, ValidateIf, IsDecimal, IsArray, ValidateNested, IsNumber, IsInt, IsObject, IsNotEmptyObject } from "class-validator";
import { BaseDto } from "src/base.dto";
import { Constants } from "src/Constants";


export class AddressDto {
    @ApiProperty({ nullable: true}) @IsOptional() @IsString() line1: string;
    @ApiProperty({ nullable: true}) @IsOptional() @IsString() city: string;
    @ApiProperty() @IsNumber() @IsNotEmpty() state_id: number;
    @ApiProperty() @IsString() @IsNotEmpty() zip: string;

}
export class SalesTaxOrderDto {
    @ApiProperty() @IsNotEmpty() @IsDecimal() price: string;
    @ApiProperty() @IsNotEmpty() @IsString() freight_term: string;
    @ApiProperty() @IsNotEmptyObject() @IsObject() @ValidateNested({ each: true }) @Type(() => AddressDto) shipping_details: AddressDto;
}

export class SaveSalesTaxOrderDto extends BaseDto {
    @Type(() => SalesTaxOrderDto)
    @ApiProperty() data: SalesTaxOrderDto;
}