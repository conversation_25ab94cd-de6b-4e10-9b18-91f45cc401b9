import { BaseDto } from "@bryzos/base-library";
import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsString, IsNotEmpty, IsNumber } from "class-validator";

export class ChatRoomDto {
    @ApiProperty() @IsString() @IsNotEmpty() defaultNotificationEnabled: string;
    @ApiProperty() @IsString() @IsNotEmpty() fileSharingMode: string;
    @ApiProperty() @IsString() @IsNotEmpty() chatRoomPermissionLevel: string;
    @ApiProperty() @IsString() @IsNotEmpty() enableChannels: string;
    @ApiProperty() @IsString() @IsNotEmpty() preModeratedChatRoom: string;
    @ApiProperty() @IsString() @IsNotEmpty() roomPassword: string;
    @ApiProperty() @IsString() @IsNotEmpty() passwordProtected: string;
    @ApiProperty() @IsString() @IsNotEmpty() description: string;
    @ApiProperty() @IsString() @IsNotEmpty() name: string;
}
export class CreateChatRoomDto extends BaseDto {
    @Type(() => ChatRoomDto)
    @ApiProperty() data: ChatRoomDto;
}

export class ChannelAddUsersDto {
    @ApiProperty() @IsString() @IsNotEmpty() channel_name: string;
    @ApiProperty() @IsString() @IsNotEmpty() buyer_id: string;
    @ApiProperty() @IsString() @IsNotEmpty() seller_id: string;
    @ApiProperty() @IsString() @IsNotEmpty() moderator_id: string;
}
export class CreateChannelAddUsersDto extends BaseDto {
    @Type(() => ChannelAddUsersDto)
    @ApiProperty() data: ChannelAddUsersDto;
}