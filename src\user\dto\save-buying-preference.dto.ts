import { Type } from "class-transformer";
import { ApiProperty } from "@nestjs/swagger";
import { <PERSON>Array, IsEmail, IsNotEmpty, IsNotEmptyObject, IsNumber, IsObject, IsOptional, IsString, ValidateNested, IsEnum, IsInt, MaxLength, IsBoolean, IsDecimal } from "class-validator";
import { AddressDto, BaseDto } from "@bryzos/extended-widget-library";
import { Constants } from "src/Constants";

export class ResaleCertificateDto {
    @ApiProperty() @IsNumber() @IsNotEmpty() state_id: number;
    @ApiProperty() @IsString() @IsNotEmpty() expiration_date: string;
    @ApiProperty() @IsString() @IsNotEmpty() cerificate_url_s3: string;
    @ApiProperty() @IsString() @IsNotEmpty() file_name: string;
    @ApiProperty() @IsOptional() @IsString() @IsNotEmpty() status?: string
    @ApiProperty() @IsOptional() @IsString() @IsNotEmpty() id?: string
}

export class DeliveryAvailibilityDto {
    @ApiProperty() @IsString() @IsNotEmpty() day: string;
    @ApiProperty() @IsString() @IsNotEmpty() from: string;
    @ApiProperty() @IsString() @IsNotEmpty() to: string;
}
export class BuyingPreferenceDto {
    @ApiProperty() @IsString() @IsNotEmpty() company_name: string;
    @ApiProperty() @IsOptional() @IsString() @IsNotEmpty() client_company: string;
    @ApiProperty() @IsNotEmptyObject() @IsObject() @ValidateNested({ each: true }) @Type(() => AddressDto) address?: AddressDto;
    @ApiProperty() @IsString() @IsNotEmpty() first_name: string;
    @ApiProperty() @IsString() @IsNotEmpty() last_name: string;
    @ApiProperty() @IsString() @IsNotEmpty() @IsEmail() email_id: string;
    @ApiProperty() @IsString() @IsNotEmpty() phone: string;

    @ApiProperty() @IsNotEmptyObject() @IsObject() @ValidateNested({ each: true }) @Type(() => AddressDto) delivery_address: AddressDto;

    @ApiProperty() @IsNumber() @IsNotEmpty() delivery_days_add_value: number;
    @ApiProperty() @IsString() @IsNotEmpty() send_invoices_to: string;
    @ApiProperty() @IsString() @IsNotEmpty() shipping_docs_to: number;

    @ApiProperty({ nullable: true, type: () => [ResaleCertificateDto] }) @IsOptional() @IsArray() 
    @ValidateNested({ each: true }) @Type(() => ResaleCertificateDto) resale_certificate?: ResaleCertificateDto[];

    @ApiProperty({ nullable: true, type: () => [DeliveryAvailibilityDto] }) @IsOptional() @IsArray() 
    @ValidateNested({ each: true }) @Type(() => DeliveryAvailibilityDto) user_delivery_receiving_availability_details?: DeliveryAvailibilityDto[];

    @ApiProperty() @IsOptional() @IsString() @IsNotEmpty() @IsEnum([Constants.PAYMENT_METHOD_BNPL,Constants.PAYMENT_METHOD_ACH_CREDIT], { message: 'Invalid payment method' }) default_payment_method: string

    //@ApiProperty( {nullable: true} ) @ValidateIf((o) => [Constants.PAYMENT_METHOD_ACH_CREDIT].includes(o.default_payment_method)) @IsNotEmpty( {message: 'ACH credit is chosen as default payment, please fill it\'s detail or remove ACH credit from default payment'} )  @ValidateNested({ each: true }) @Type(() => AchCreditDto) ach_credit : AchCreditDto;
}

export class SaveBuyingPreferenceDto extends BaseDto {
    @Type(() => BuyingPreferenceDto)
    @ApiProperty() data: BuyingPreferenceDto;
}

export class DeleteResaleCertDto {
    @ApiProperty() @IsString() @IsNotEmpty() cert_id: string
}

export class DeleteResaleCertificateDto extends BaseDto{
    @Type(() => DeleteResaleCertDto)
    @ApiProperty() data : DeleteResaleCertDto
}

export class BuyerProfileDto {
    @ApiProperty() @IsString() @IsNotEmpty() first_name: string;
    @ApiProperty() @IsString() @IsNotEmpty() last_name: string;
    @ApiProperty() @IsString() @IsNotEmpty() @IsEmail() email_id: string;
    @ApiProperty() @IsString() @IsNotEmpty() phone: string;
}
export class SaveBuyerProfileDto extends BaseDto {
    @Type(() => BuyerProfileDto)
    @ApiProperty() data: BuyerProfileDto;
}

export class BuyerCompanyInfoDto {
    @ApiProperty() @IsNotEmpty() @IsString() company_name: string;
    @ApiProperty() @IsOptional() @IsNotEmpty() @IsString() client_company: string;
    @ApiProperty() @IsNotEmpty() @IsString() company_address_line1: string;
    @ApiProperty() @IsNotEmpty() @IsString() company_address_city: string;
    @ApiProperty() @IsNotEmpty() @IsNumber() company_address_state_id: number;
    @ApiProperty() @IsNotEmpty() @IsString() company_address_zip: string;
}

export class SaveBuyerCompanyInfoDto extends BaseDto {
    @Type(() => BuyerCompanyInfoDto)
    @ApiProperty() data: BuyerCompanyInfoDto;
}
export class BuyerDeliveryDto {
    @ApiProperty() @IsString() @IsNotEmpty() delivery_address_line1: string;
    @ApiProperty() @IsString() @IsNotEmpty() delivery_address_city: string;
    @ApiProperty() @IsNumber() @IsNotEmpty() delivery_address_state_id: number;
    @ApiProperty() @IsString() @IsNotEmpty() delivery_address_zip: string;
    @ApiProperty() @IsString() @IsNotEmpty() send_invoices_to: string;
    @ApiProperty() @IsString() @IsNotEmpty() shipping_docs_to: number;
    @ApiProperty() @IsNumber() @IsNotEmpty() delivery_days_add_value: number
}

export class SaveBuyerDeliveryDto extends BaseDto {
    @Type(() => BuyerDeliveryDto)
    @ApiProperty() data: BuyerDeliveryDto;
}

export class BnplRequestDto {
    @ApiProperty() @IsString() @IsNotEmpty() @MaxLength(10) ein_number: string;
    @ApiProperty() @IsString() @IsNotEmpty() @MaxLength(9) duns_number: string;
    @ApiProperty() @IsString() @IsNotEmpty() @IsDecimal() desired_credit_limit: string;
    @ApiProperty() @IsInt() @IsNotEmpty() pgpm_mapping_id: number;
    @ApiProperty() @IsString() @IsNotEmpty() reference_document_id: string;
}
export class SaveBnplRequestDto extends BaseDto {
    @Type(() => BnplRequestDto)
    @ApiProperty() data : BnplRequestDto
}

export class SaveBuyerReceivingHrsDto {
    @IsArray() @ValidateNested({ each: true }) @Type(() => DeliveryAvailibilityDto) data: DeliveryAvailibilityDto[] 
}
export class BuyerDucumentLibDto {
    @ApiProperty({ type: () => [ResaleCertificateDto] }) @IsNotEmpty() @IsArray() 
    @ValidateNested({ each: true }) @Type(() => ResaleCertificateDto) resale_certificate?: ResaleCertificateDto[];
}
export class SaveBuyerDucumentLibDto {
    @IsArray() @ValidateNested({ each: true }) @Type(() => ResaleCertificateDto) data: ResaleCertificateDto[] 
}