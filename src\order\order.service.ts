import { Injectable } from '@nestjs/common';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { DataBaseService, BaseLibraryService } from '@bryzos/base-library';
import { Repository } from 'typeorm';
import { UserPurchaseOrder, UserViewedPurchaseOrder, ReferenceDataStates, ReferenceDataSalesTax, UserResaleCertificate, ReferenceDataOrderStatus } from '@bryzos/extended-widget-library';
import { InjectRepository } from '@nestjs/typeorm';
import { Constants } from 'src/Constants';

@Injectable()
export class OrderService {
  private dbServiceObj = new DataBaseService()
  
  constructor(
    private readonly baseLibraryService : BaseLibraryService,
    @InjectRepository(UserPurchaseOrder) private readonly userPurchaseOrderRepository: Repository<UserPurchaseOrder>,
    @InjectRepository(UserViewedPurchaseOrder) private readonly userViewedPurchaseOrderRepository: Repository<UserViewedPurchaseOrder>,
    @InjectRepository(ReferenceDataStates) private readonly refStates: Repository<ReferenceDataStates>,
    @InjectRepository(ReferenceDataSalesTax) private readonly refSalesTax: Repository<ReferenceDataSalesTax>,
    @InjectRepository(UserResaleCertificate) private readonly userReasaleCertificateRepository: Repository<UserResaleCertificate>,
    @InjectRepository(ReferenceDataOrderStatus) private readonly userReferenceDataOrderStatusRepository: Repository<ReferenceDataOrderStatus>
  ){}
  create(createOrderDto: CreateOrderDto) {
    return 'This action adds a new order';
  }

  findAll() {
    return `This action returns all order`;
  }

  findOne(id: number) {
    return `This action returns a #${id} order`;
  }

  update(id: number, updateOrderDto: UpdateOrderDto) {
    return `This action updates a #${id} order`;
  }

  remove(id: number) {
    return `This action removes a #${id} order`;
  }

  async saveOrderViewed(data,userId) {
    let response = "successfully updated";
    await this.dbServiceObj.markInActiveMultipleWhere(this.userViewedPurchaseOrderRepository, {'user_id': userId, 'user_purchase_order_id': data.id});

    await this.dbServiceObj.saveWithOutMapper({'user_id': userId, 'user_purchase_order_id': data.id }, userId, this.userViewedPurchaseOrderRepository);

    return response;
  }

  async salesTaxCalculate(userId,payload){

    let salesTaxCounter = null;
    if(payload.salesTaxCounter){
      salesTaxCounter = payload.salesTaxCounter
    }
    let salesTax = 0;

    const freightTerm = payload.freight_term;
    const delivery_state_id = payload.shipping_details.state_id;
    const zipCode = payload.shipping_details.zip;
 
    
    if (freightTerm === Constants.FREIGHTTERMDELIVERED && delivery_state_id && zipCode) { //Frieght Term is always Delivered in GISS
      
      const resaleCertData = await this.dbServiceObj.findOneByMultipleWhere(this.userReasaleCertificateRepository,{user_id:userId,state_id:delivery_state_id});

      const salesTaxData = await this.baseLibraryService.calculateSalesTaxRate(delivery_state_id, zipCode, resaleCertData, this.userPurchaseOrderRepository);
      let salesTaxRate = 0;
      if(salesTaxData){
        salesTaxRate = salesTaxData.sales_tax_rate;
      }
      const cart_items = payload.cart_items;
      
      if(cart_items.length > 0 && salesTaxRate){
        for(const cart_item of cart_items){
          const calculateSalesTax = Number(cart_item.extended) * Number(salesTaxRate);
          salesTax += parseFloat(calculateSalesTax.toFixed(2));
        }
      }
    }
    return {"tax" : salesTax,"salesTaxCounter" : salesTaxCounter};
  }
}