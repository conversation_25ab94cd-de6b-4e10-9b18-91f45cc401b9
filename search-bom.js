const mysql = require('mysql');
const _ = require('lodash');

// Define the connection to your MySQL database
const connection = mysql.createConnection({
  host: 'localhost', // Your MySQL host
  user: 'root',      // Your MySQL username
  password: '',      // Your MySQL password
  database: 'your_database_name' // Your MySQL database name
});

// Sample JSON data
const jsonData = [
  {
    "Product Tag": "M42040000",
    "Description": "FLAT BAR A-36 . 1046\" X 2\" X 16'",
    "Quantity": "",
    "Specification": "A-36",
    "Grade": "",
    "Length": "16'"
  },
  {
    "Product Tag": "M42040039",
    "Description": "FLAT BAR A-36 1/4 X 8 X 20'",
    "Quantity": "",
    "Specification": "A-36",
    "Grade": "",
    "Length": "20'"
  },
  {
    "Product Tag": "M42040001",
    "Description": "FLAT BAR A-36 1/8 X 2 X 20'",
    "Quantity": "",
    "Specification": "A-36",
    "Grade": "",
    "Length": "20'"
  }
];

// Function to find the best matching product ID
function findBestMatch(jsonItem) {
  const query = 'SELECT * FROM reference_data_products_widget WHERE is_active = 1';
  
  connection.query(query, (error, results) => {
    if (error) {
      console.error("Error executing query:", error);
      connection.end();
      return;
    }

    let bestMatch = null;
    let highestMatchScore = 0;

    // Iterate through each record in the table to compare with the JSON data
    results.forEach((row) => {
      let matchScore = 0;

      // Compare Description with Key1
      if (row.Key1 && row.Key1.toLowerCase().includes(jsonItem.Description.toLowerCase())) {
        matchScore++;
      }

      // Compare Specification with Key4
      if (row.Key4 && row.Key4.toLowerCase().includes(jsonItem.Specification.toLowerCase())) {
        matchScore++;
      }

      // Compare Length with Key9 or Key10 (you can customize which Key to compare)
      if (row.Key9 && row.Key9.toLowerCase().includes(jsonItem.Length.toLowerCase())) {
        matchScore++;
      }

      // If the match score is higher, update the best match
      if (matchScore > highestMatchScore) {
        highestMatchScore = matchScore;
        bestMatch = row.Product_ID;
      }
    });

    // Log the result for the current JSON item
    console.log({
      "Product Tag": jsonItem["Product Tag"],
      "Best Match Product ID": bestMatch
    });
  });
}

// Process each item in the JSON data
jsonData.forEach((jsonItem) => {
  findBestMatch(jsonItem);
});
