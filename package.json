{"name": "widget-service", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "main": "main.js", "engines": {"node": "18.16.0"}, "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "node dist/src/main.js", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "dev": "cross-env NODE_ENV=development nest start --watch", "demo": "cross-env NODE_ENV=demo nest start --watch", "prebuild": "<PERSON><PERSON><PERSON> dist", "build:prod": "set NODE_ENV=production&& nest build"}, "dependencies": {"@aws-sdk/client-textract": "3.600.0", "@bryzos/base-library": "1.3.45", "@bryzos/extended-widget-library": "1.2.80", "@bryzos/pdf-maker": "1.1.8", "@nestjs/common": "^9.0.0", "@nestjs/config": "^2.3.1", "@nestjs/core": "^9.0.0", "@nestjs/mapped-types": "*", "@nestjs/platform-express": "^9.0.0", "@nestjs/swagger": "^6.2.1", "@nestjs/typeorm": "^9.0.1", "amazon-cognito-identity-js": "6.3.6", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "date-fns": "^2.29.3", "date-fns-tz": "^2.0.0", "exceljs": "^4.3.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.1.2", "node-loggly-bulk": "^3.0.1", "raygun": "^0.13.2", "reflect-metadata": "^0.1.13", "rxjs": "^7.2.0", "typeorm": "^0.3.12", "uuid": "9.0.1", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^9.0.0", "@nestjs/schematics": "^9.0.0", "@nestjs/testing": "^9.0.0", "@types/express": "^4.17.13", "@types/jest": "29.2.4", "@types/node": "18.11.18", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "cross-env": "7.0.3", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "jest": "29.3.1", "prettier": "^2.3.2", "run-script-webpack-plugin": "^0.1.1", "source-map-support": "^0.5.20", "supertest": "^6.1.3", "ts-jest": "29.0.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "4.1.1", "typescript": "^4.7.4", "webpack": "^5.75.0", "webpack-node-externals": "^3.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}