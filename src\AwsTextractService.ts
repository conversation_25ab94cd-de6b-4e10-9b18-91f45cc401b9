import { AwsUtilityV3, <PERSON>LibraryService, <PERSON><PERSON>zosLogger } from "@bryzos/base-library";
import { Injectable } from "@nestjs/common";
import  { TextractClient, AnalyzeDocumentCommand, FeatureType, StartDocumentTextDetectionCommand, StartDocumentAnalysisCommand, GetDocumentAnalysisCommand, StartExpenseAnalysisCommand, GetDocumentTextDetectionCommand, GetExpenseAnalysisCommand } from "@aws-sdk/client-textract";
import { log } from "console";

@Injectable()
export class AwsTextractService {

    private awsAccessKeys: { AWS_CREDENTIAL_KEY:string, AWS_CREDENTIAL_SECRET:string }
    private textractClient:any;
    private readonly region = process.env.AWS_REGION;
    private smEnv: string;
    private smCodedVendor: string;
    private smCodedKey: string;

    constructor(
        private readonly baseService:BaseLibraryService,
        private readonly awsUtility:AwsUtilityV3
      ) {
        this.smEnv = process.env.SM_ENV;
        this.smCodedVendor = 'AWS';
        this.smCodedKey = 'CREDENTIAL';
        this.getAwsKeys();
      }
    
    async getAwsKeys(){
        try {
            let keys = await this.baseService.getSecretValue(this.smEnv,this.smCodedVendor,this.smCodedKey);
            if(!keys){ return false; }
            this.awsAccessKeys = keys;
            console.log(this.awsAccessKeys);
        } catch(error){
            console.log("CognitoAutService error", error);
        }
    }

    async setTextractClientClient() {
        const awsKeys = this.awsAccessKeys; 
        return this.textractClient = new TextractClient({
          region: this.region,
          credentials: {
            accessKeyId: awsKeys.AWS_CREDENTIAL_KEY,
            secretAccessKey: awsKeys.AWS_CREDENTIAL_SECRET,
          },
        });
    }
    
  async callAsyncExtractTextApi(documentName: string) {
    const processType = "ANALYSIS";
    let response = await this.asyncExtractText(documentName, processType);
    return response;
  }
    
  async asyncExtractText(documentName: string, processType: string) {
    await this.setTextractClientClient();
    const bucket = process.env.EXTRACT_INVOICE_S3_BUCKET;
    const roleArn = "arn:aws:iam::245262708136:role/TextractRole";
    const sqsQueueUrl = process.env.EXTRACT_INVOICE_QUEUE_URL;
    const snsTopicArn = 'arn:aws:sns:us-east-1:245262708136:InvoiceTextExtraction';
    var startJobId = "";
    
    try {
      var jobFound = false;
      var succeeded = false;
      var dotLine = 0;
      var validType = false;

      if (processType === "ANALYSIS"){
        const { JobId } = await this.textractClient.send(new StartDocumentAnalysisCommand({DocumentLocation:{S3Object:{Bucket:bucket, Name:documentName}},FeatureTypes: [FeatureType.TABLES, FeatureType.LAYOUT], NotificationChannel:{RoleArn: roleArn, SNSTopicArn: snsTopicArn}}))
        return JobId;
      }

      if (validType == false){
        console.log("Invalid processing type. Choose Detection or Analysis."); return
      }

      while (jobFound == false) {
        var sqsReceivedResponse = await this.awsUtility.readSQSQueues(sqsQueueUrl);        
        if (sqsReceivedResponse){
          var responseString = JSON.stringify(sqsReceivedResponse)
          if (!responseString.includes('Body')){
            if (dotLine < 40) {
              dotLine = dotLine + 1
            }else {
              dotLine = 0 
            };
            await new Promise(resolve => setTimeout(resolve, 5000));
            continue
          }
        }

        // Once job found, log Job ID and return true if status is succeeded
        for (var message of sqsReceivedResponse.Messages) {
          var notification = JSON.parse(message.Body)
          var rekMessage = JSON.parse(notification.Message)
          var messageJobId = rekMessage.JobId
        console.log(messageJobId);
        
          if (String(rekMessage.JobId).includes(String(startJobId))) {
            jobFound = true
            var operationResults = await this.getResults(processType, rekMessage.JobId)
            if (String(rekMessage.Status).includes(String("SUCCEEDED"))) {
              succeeded = true
              await this.awsUtility.deleteDataFromSQS(sqsReceivedResponse, sqsQueueUrl);
            }
          } else {
            console.log("Provided Job ID did not match returned ID.")
            await this.awsUtility.deleteDataFromSQS(sqsReceivedResponse, sqsQueueUrl);
          }
        }
        return operationResults;
      } 
    } catch (error) {
      console.log("Invoice extract error", error);
    }

  }
    
  async getResults(processType:string, JobID:string) {
    await this.setTextractClientClient();

    var maxResults = 1000;
    var paginationToken = null;
    var finished = false;
    let originalResponse = [];
    while (finished == false) {
      var response = null;

      if (processType == 'ANALYSIS') {
        if (paginationToken == null){
          response = await this.textractClient.send(new GetDocumentAnalysisCommand({JobId:JobID, MaxResults:maxResults}))
        } else {
          response = await this.textractClient.send(new GetDocumentAnalysisCommand({JobId:JobID, MaxResults:maxResults, NextToken:paginationToken}))
        }
      }

      if(processType == 'DETECTION') {
        if (paginationToken == null){
          response = await this.textractClient.send(new GetDocumentTextDetectionCommand({JobId:JobID, MaxResults:maxResults}))
      
        }else{
          response = await this.textractClient.send(new GetDocumentTextDetectionCommand({JobId:JobID, MaxResults:maxResults, NextToken:paginationToken}))
        }
      }

      if(processType == 'EXPENSE') {
        if (paginationToken == null){
          response = await this.textractClient.send(new GetExpenseAnalysisCommand({JobId:JobID, MaxResults:maxResults}))
      
        }else{
          response = await this.textractClient.send(new GetExpenseAnalysisCommand({JobId:JobID, MaxResults:maxResults, NextToken:paginationToken}))
        }
      }

      await new Promise(resolve => setTimeout(resolve, 5000));
// console.log("response : ", response);

      originalResponse.push(...response.Blocks);

      if(response.NextToken){
        paginationToken = response.NextToken
      }else{
        finished = true
      }

      console.log("finished : ", finished);
    }

    var totalPages = response?.DocumentMetadata?.Pages || 0;
    
    return {
      original_json : originalResponse,
      total_page: totalPages
    }
  }
    
  async catchErrorLogs(error:any) {
    const currentDateTime = new Date();
    const UtcTime = currentDateTime.toISOString();
    const logglyData = JSON.stringify({'date': UtcTime,'errorMessage': error?.message,'data': error?.stack});
    console.log(process.env.NODE_ENV+'_'+'AWS_TEXTRACT_SERVICE_ERROR');
    BryzosLogger.log(logglyData, process.env.NODE_ENV+'_'+'AWS_TEXTRACT_SERVICE_ERROR');
  }

  // Poll until job completes and return all results
  async getAnalysisResults(jobId) {
    await this.setTextractClientClient();

    // Wait for job to complete
    let res;
    do {
      await new Promise((r) => setTimeout(r, 5000));
      res = await this.textractClient.send(
        new GetDocumentAnalysisCommand({ JobId: jobId })
      );
      console.log('Textract job status:', res.JobStatus);
    } while (res.JobStatus === 'IN_PROGRESS');

    if (res.JobStatus !== 'SUCCEEDED')
      throw new Error(`Textract job failed: ${res.JobStatus}`);

    // Collect all blocks from all response pages
    let allBlocks = [...res.Blocks];
    let nextToken = res.NextToken;

    // Continue fetching if there are more pages of results
    while (nextToken) {
      console.log('Fetching next page of Textract results...');
      res = await this.textractClient.send(
        new GetDocumentAnalysisCommand({
          JobId: jobId,
          NextToken: nextToken,
        })
      );
      allBlocks = [...allBlocks, ...res.Blocks];
      nextToken = res.NextToken;
      console.log(
        `Retrieved ${res.Blocks.length} more blocks, total now: ${allBlocks.length}`
      );
    }

    // Create a complete response object
    const completeResponse = {
      JobId: jobId,
      Status: 'SUCCEEDED',
      DocumentMetadata: res.DocumentMetadata,
      Blocks: allBlocks,
    };

    return completeResponse;
  }

}