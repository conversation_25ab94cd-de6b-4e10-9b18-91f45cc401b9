import { Controller, Get, Post, Body, Patch, Param, Delete, Response, ValidationPipe, UsePipes } from '@nestjs/common';
import { HomePageService } from './home-page.service';
import { SaveHomePageShareProductPricing, SaveHomePagePricingFeedbackDto } from './dto/home-page.dto';
import { Constants } from 'src/Constants';
const payloadTag = Constants.PAYLOAD_TAG;
const responseTag = Constants.RESPONSE_TAG;

@Controller('homepage')
export class HomePageController {
  constructor(private readonly homePageService: HomePageService) {}

  @Post('/shareProductPrice')
  @UsePipes(ValidationPipe)
  async homePageShareProductPricing(@Body() homepageShareProductPricingDto: SaveHomePageShareProductPricing, @Response() res) {
    let payloadData = homepageShareProductPricingDto[payloadTag];
    let responseData = {
      [responseTag]: await this.homePageService.homePageShareProductPricing(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/savePricingFeedback')
  @UsePipes(ValidationPipe)
  async homePageSavePricingFeedback(@Body() homePagePricingFeedback: SaveHomePagePricingFeedbackDto, @Response() res) {
    let payloadData = homePagePricingFeedback[payloadTag];
    let responseData = {
      [responseTag]: await this.homePageService.homePageSavePricingFeedback(payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('/searchAnalytics')
  async homePageSearch(@Body() payloadData, @Response() res) {
    let payload = payloadData[payloadTag];
    let responseData = {
      [responseTag]: await this.homePageService.homePageSaveSearchAnalytics(payload)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
}
