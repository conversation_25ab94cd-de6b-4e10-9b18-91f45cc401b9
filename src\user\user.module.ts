import { MiddlewareConsumer, Module } from '@nestjs/common';
import { UserController } from './user.controller';
import { LoggerMiddleware } from '@bryzos/extended-widget-library';
import { PermissionMiddleware } from '@bryzos/base-library';
import { SharedModule } from 'src/shared.module';

// @Module({
//   imports : [BaseLibraryModule,DataBaseService,TypeOrmModule.forFeature(OConstants.EntityArray)],
//   controllers: [UserController],
//   providers: OConstants.ServiceArray
// })


@Module({
  imports: [SharedModule],
  controllers: [UserController],
  providers: [SharedModule],
  exports: [SharedModule],
})

export class UserModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
    .apply(LoggerMiddleware)
    .forRoutes('/user');
    consumer
    .apply(PermissionMiddleware)
    .exclude('/user/onBoard','/user/verifyOnBoardUserEmail','/user/verifyZipCode','user/company')
    .forRoutes('/user');
	}
}
