import { ApiProperty,PartialType } from '@nestjs/swagger';
import { CreateReferenceDatumDto } from './create-reference-datum.dto';
import { Type } from "class-transformer";
import { IsNotEmpty, IsEmail, IsString, IsEnum, IsOptional, ValidateIf, IsDecimal, IsArray, ValidateNested, IsNumber, IsInt, IsObject } from "class-validator";
import { BaseDto } from "src/base.dto";

export class UpdateReferenceDatumDto extends PartialType(CreateReferenceDatumDto) {}
export class WidgetTermsCondtionProductActionsDto {
    @ApiProperty() @IsNotEmpty() @IsNumber()id: number;
    @ApiProperty() @IsNotEmpty() @IsNumber()Shape_ID: number;
    @ApiProperty() @IsNotEmpty() @IsNumber()Size_Group_ID: number;
    @ApiProperty() @IsNotEmpty() @IsNumber()Product_ID: number;
    @ApiProperty() @IsNotEmpty() @IsString()Bucket: string;
    @ApiProperty() @IsNotEmpty() @IsString()LBS_FT: string;
    @ApiProperty() @IsOptional() @IsString()Key1: string;
    @ApiProperty() @IsOptional() @IsString()Key2: string;
    @ApiProperty() @IsOptional() @IsString()Key3: string;
    @ApiProperty() @IsOptional() @IsString()Key4: string;
    @ApiProperty() @IsOptional() @IsString()Key5: string;
    @ApiProperty() @IsOptional() @IsString()Key6: string;
    @ApiProperty() @IsOptional() @IsString()Key7: string;
    @ApiProperty() @IsOptional() @IsString()Key8: string;
    @ApiProperty() @IsOptional() @IsString()Key9: string;
    @ApiProperty() @IsOptional() @IsString()Key10: string;
    @ApiProperty() @IsOptional() @IsString()Key11: string;
    @ApiProperty() @IsOptional() @IsString()Key12: string;
    @ApiProperty() @IsOptional() @IsString()Key13: string;
    @ApiProperty() @IsOptional() @IsString()Key14: string;
    @ApiProperty() @IsOptional() @IsString()Key15: string;
    @ApiProperty() @IsOptional() @IsString()Key16: string;
    @ApiProperty() @IsOptional() @IsString()Key17: string;
    @ApiProperty() @IsOptional() @IsString()Key18: string;
    @ApiProperty() @IsOptional() @IsString()Key19: string;
    @ApiProperty() @IsOptional() @IsString()Key20: string;
    @ApiProperty() @IsOptional() @IsString()Key21: string;
    @ApiProperty() @IsOptional() @IsString()Key22: string;
    @ApiProperty() @IsOptional() @IsString()Key23: string;
    @ApiProperty() @IsOptional() @IsString()Key24: string;
    @ApiProperty() @IsNotEmpty() @IsString()UI_Description: string;
    @ApiProperty() @IsNotEmpty() @IsString()Neutral_Pricing_LB: string;
    @ApiProperty() @IsNotEmpty() @IsString()Neutral_Pricing_Ft: string;
    @ApiProperty() @IsNotEmpty() @IsString()Buyer_Pricing_LB: string;
    @ApiProperty() @IsNotEmpty() @IsString()Buyer_Pricing_Ft: string;
    @ApiProperty() @IsNotEmpty() @IsString()Seller_Pricing_LB: string;
    @ApiProperty() @IsNotEmpty() @IsString()Seller_Pricing_Ft: string;
}
export class SaveWidgetTermsCondtionProductActionsDto extends BaseDto {
    @Type(() => WidgetTermsCondtionProductActionsDto)
    @ApiProperty() data: WidgetTermsCondtionProductActionsDto;
}

export class GetCacheData{
    @ApiProperty() @IsNotEmpty() @IsString()type: string;
}

export class GetCacheDataDto extends BaseDto {
    @Type(() => GetCacheData)
    @ApiProperty() data: GetCacheData;
}
export class ReferenceDataShapeIDType {
    @ApiProperty() @IsNotEmpty() @IsNumber({}, { message: " The Shape Id's value must be an number format." }) v : number;
}
export class ReferenceDataSizeGroupIDType {
    @ApiProperty() @IsNotEmpty() @IsNumber({}, { message: " The Size Groups Id's value must be an number format." }) v : number;
}
export class ReferenceDataProductIDType {
    @ApiProperty() @IsNotEmpty() @IsNumber({}, { message: " The Product Id's value must be an number format." }) v : number;
}

export class ReferenceDataProductUploadDto {
    @ApiProperty({ type: () => [ReferenceDataShapeIDType] }) @IsObject() @ValidateNested({ each: true }) @Type(() => ReferenceDataShapeIDType) Shape_ID: ReferenceDataShapeIDType[];
    @ApiProperty({ type: () => [ReferenceDataSizeGroupIDType] }) @IsObject() @ValidateNested({ each: true }) @Type(() => ReferenceDataSizeGroupIDType) Size_Group_ID: ReferenceDataSizeGroupIDType[];
    @ApiProperty({ type: () => [ReferenceDataProductIDType] }) @IsObject() @ValidateNested({ each: true }) @Type(() => ReferenceDataProductIDType) Product_ID: ReferenceDataProductIDType[];
}
export class SaveReferenceDataProductUploadDto extends BaseDto {
    @Type(() => ReferenceDataProductUploadDto)
    @ApiProperty() @IsArray() data: ReferenceDataProductUploadDto;
}

export class searchProductDto {
    @ApiProperty() @IsString() @IsNotEmpty() keyword: string;
}
export class searchProductsDto extends BaseDto {
    @Type(() => searchProductDto)
    @ApiProperty() data: searchProductDto;
}