export const configuration = () => ({
    NODE_ENV: process.env.NODE_ENV,
    PORT: parseInt(process.env.PORT, 10) || 8080,
    API_KEY:process.env.API_KEY,
    MASTER_DB:process.env.MASTER_DB,

    LOGGLY_TOKEN:process.env.LOGGLY_TOKEN,
    LOGGLY_SUBDOMAIN:process.env.LOGGLY_SUBDOMAIN,
    LOGGLY_USERNAME:process.env.LOGGLY_USERNAME,
    LOGGLY_PASSWORD:process.env.LOGGLY_PASSWORD,
    LOGGLY_ERROR_TAG:process.env.LOGGLY_ERROR_TAG,
    LOGGLY_REQUEST_RESPONSE_TAG:process.env.LOGGLY_REQUEST_RESPONSE_TAG,

    RAYGUN_API:process.env.RAYGUN_API,
    
    AWS_REGION:process.env.AWS_REGION,
    AWS_USERPOOL_ID:process.env.AWS_USERPOOL_ID,
    AWS_VERSION:process.env.AWS_VERSION,
    AWS_SIGNATURE_VERSION:process.env.AWS_SIGNATURE_VERSION,

    REQUEST_JSON_SIZE:process.env.REQUEST_JSON_SIZE,
    SM_BASE_URL:process.env.SM_BASE_URL,
    SM_API_KEY:process.env.SM_API_KEY,
    SM_BASE_URL_USER_AGENT:process.env.SM_BASE_URL_USER_AGENT,
    SM_ENV:process.env.SM_ENV,
    VALUE_ENCRYPTION_TYPE:process.env.VALUE_ENCRYPTION_TYPE,

    TRUVALUT_BUYER_URL:process.env.TRUVALUT_BUYER_URL,
    TRUVALUT_SELLER_URL:process.env.TRUVALUT_SELLER_URL,
    TRUVALUT_API_KEY:process.env.TRUVALUT_API_KEY,
    TRUVALUT_ACCESS_KEY:process.env.TRUVALUT_ACCESS_KEY,
    UI_ORIGIN:process.env.TRUVALUT_ACCESS_KEY,
    SERVICE_ORIGIN:process.env.TRUVALUT_ACCESS_KEY
  });