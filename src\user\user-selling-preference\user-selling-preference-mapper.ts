export class ArPaymentInfoMapper {

    public roundOffFields = [];
    public dateFields = [];

    public MappingJson = {
        "bank_name": "bank_name",
        "routing_number": "routing_number",
        "account_number": "account_number",
        "reference_document_id": "reference_document_id",
        "pgpm_mapping_id": "pgpm_mapping_id",
        "payment_info_id": "payment_info_id"
    };
}

export class PaymentInfoMapper {

    public roundOffFields = [];
    public dateFields = [];

    public MappingJson = {
        "user_id": "user_id",
        "pgpm_mapping_id": "pgpm_mapping_id",
        "pg_vendor_id": "pg_vendor_id"
    };
}

export class SellingPreferenceMapper {

    public MappingJson = {
        "company_address_line1": {
            "address": "line1"
        },
        "company_address_line2": {
            "address": "line2"
        },
        "company_address_city": {
            "address": "city"
        },
        "company_address_state_id": {
            "address": "state_id"
        },
        "company_address_zip": {
            "address": "zip"
        },
        "company_name": "company_name",
        "company_id": "company_id",
        "first_name": "first_name",
        "last_name": "last_name",
        "email_id": "email_id",
        "phone": "phone",
        "stocking_location": "stocking_location",
        "send_invoices_to": "send_invoices_to",
        "shipping_docs_to": "shipping_docs_to",
        "w9_form_s3_url": "w9_form_s3_url",
        "products_s3_url": "products_s3_url",
        "client_company": "client_company"
    }

}