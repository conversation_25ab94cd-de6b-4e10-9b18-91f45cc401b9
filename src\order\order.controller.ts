import { Controller, Get, Post, Body, Patch, Param, Delete, UsePipes, Response, ValidationPipe, Headers } from '@nestjs/common';
import { ReferenceDataSalesTax } from '@bryzos/extended-widget-library';
import { OrderService } from './order.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { SaveSalesTaxOrderDto } from './dto/order.dto';
import { Constants } from '@bryzos/base-library';


const payloadTag = Constants.PAYLOAD_TAG;
const responseTag = Constants.RESPONSE_TAG;

@Controller('order')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Post()
  create(@Body() createOrderDto: CreateOrderDto) {
    return this.orderService.create(createOrderDto);
  }

  @Get()
  findAll() {
    return this.orderService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.orderService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateOrderDto: UpdateOrderDto) {
    return this.orderService.update(+id, updateOrderDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.orderService.remove(+id);
  }

  
  @Post('/seller-viewed-order')
  async saveOrderViewd(@Body() payload: any, @Response() res) {
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.orderService.saveOrderViewed(payload[payloadTag],userId)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }

  @Post('sales_tax_calculate')
  @UsePipes(ValidationPipe)
  async salesTax(@Body() referencedata:SaveSalesTaxOrderDto, @Response() res){
    let payloadData = referencedata[payloadTag];
    let userId = res.locals.authorizedUserId;
    let responseData = {
      [responseTag]: await this.orderService.salesTaxCalculate(userId,payloadData)
    };
    res.locals.responseData = responseData;
    return res.status(Constants.RESPONSE_CODE_OK).json(responseData);
  }
}


