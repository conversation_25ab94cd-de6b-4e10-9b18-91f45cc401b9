import { Test, TestingModule } from '@nestjs/testing';
import { ExternalAffairsController } from './external-affairs.controller';
import { ExternalAffairsService } from './external-affairs.service';

describe('ExternalAffairsController', () => {
  let controller: ExternalAffairsController;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ExternalAffairsController],
      providers: [ExternalAffairsService],
    }).compile();

    controller = module.get<ExternalAffairsController>(ExternalAffairsController);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
