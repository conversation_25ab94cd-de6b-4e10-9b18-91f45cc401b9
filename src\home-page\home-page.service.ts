import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { AwsQueue } from 'src/AwsQueue';
import { Constants } from 'src/Constants';
import { Repository } from 'typeorm';
import { DataBaseService } from '@bryzos/base-library';
import { HomePageShareProductPricing, HomePagePricingFeedbackDto } from './dto/home-page.dto';
import { HomepagePricingFeedback,HomepageSearchAnalytics,HomepageShareWidgetRequest,HomepageShareProductPricing } from '@bryzos/extended-widget-library'
@Injectable()
export class HomePageService {
  private dbServiceObj = new DataBaseService()

  constructor(
    private readonly awsQueue:AwsQueue,
    @InjectRepository(HomepagePricingFeedback) private readonly homePagePricingFeedback: Repository<HomepagePricingFeedback>,
    @InjectRepository(HomepageShareWidgetRequest) private readonly homepageShareWidgetRequest: Repository<HomepageShareWidgetRequest>,
    @InjectRepository(HomepageShareProductPricing) private readonly homepageShareProductPricing: Repository<HomepageShareProductPricing>,
    @InjectRepository(HomepageSearchAnalytics) private readonly homepageSearchAnalyticsRepository: Repository<HomepageSearchAnalytics>,
  ) {}
    
  async homePageShareProductPricing (homepageShareProductPricingDto: HomePageShareProductPricing) {
    let response = null;
      if(homepageShareProductPricingDto.to_email == homepageShareProductPricingDto.from_email) return { "error_message": "To and from email cannot be same" };
      let shareProductPricing = await this.dbServiceObj.saveData(homepageShareProductPricingDto,this.homepageShareProductPricing);
      if(shareProductPricing) {
        await this.awsQueue.sendDataToAwsQueue(shareProductPricing.id,Constants.HOMEPAGE_SHARE_PRODUCT_PRICING,"Home-page share product pricing",process.env.MESSAGE_SERVICE_EMAIL_QUEUE_URL);
        response = 'Homepage Product prices shared successfully'
      }
    return response;
  }

  async homePageSavePricingFeedback(homePagePricingFeedbackDto: HomePagePricingFeedbackDto) {
    let response = null;
      let feedback = await this.dbServiceObj.saveData(homePagePricingFeedbackDto,this.homePagePricingFeedback);
      if(feedback) {
        response = "Feedback noted";
      }
    return response;
  }

  async homePageSaveSearchAnalytics(payload){
    if(!payload.status){
      payload.status = Constants.NONE;
    }
    await this.dbServiceObj.saveData(payload,this.homepageSearchAnalyticsRepository);
  }
}
