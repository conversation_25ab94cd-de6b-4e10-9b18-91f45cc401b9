import { BaseDto } from "@bryzos/extended-widget-library";
import { ApiProperty } from "@nestjs/swagger";
import { Type } from "class-transformer";
import { IsNotEmpty, IsEmail, IsOptional, IsString, IsEnum } from "class-validator";

export class WebhookSubscribeDto {
    @ApiProperty() @IsNotEmpty() @IsString() webhook_url: string;
    @ApiProperty() @IsNotEmpty() @IsString() event: string;
}

export class SaveWebhookSubscribeDto extends BaseDto {
    @Type(() => WebhookSubscribeDto)
    @ApiProperty() data: WebhookSubscribeDto;
}