import { MiddlewareConsumer, Module } from '@nestjs/common';
import { OrderController } from './order.controller';
import { LoggerMiddleware } from '@bryzos/extended-widget-library';
import { PermissionMiddleware } from '@bryzos/base-library';
import { SharedModule } from 'src/shared.module';

// @Module({
//   imports : [BaseLibraryModule,DataBaseService,TypeOrmModule.forFeature(OConstants.EntityArray)],
//   controllers: [OrderController],
//   providers: OConstants.ServiceArray
// })
@Module({
  imports: [SharedModule],
  controllers: [OrderController],
  providers: [SharedModule],
  exports: [SharedModule],
})

export class OrderModule {

  configure(consumer: MiddlewareConsumer) {
		consumer
		  .apply(LoggerMiddleware, PermissionMiddleware)
		  .forRoutes('/order');
		}
}
